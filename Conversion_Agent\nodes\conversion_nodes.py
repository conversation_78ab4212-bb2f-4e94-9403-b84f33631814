# Standard library imports
import os
from typing import Dict, Any, List, Tuple

# Third-party imports
import pandas as pd

# Local imports - State
from Conversion_Agent.state import WorkflowState, ErrorContext, StatementConversionOutput, ValidationOutput
from Conversion_Agent.state.state import Phase1ErrorIdentificationOutput, Phase2ErrorContextOutput, Phase1IdentificationOutput

# Local imports - Utilities
from Conversion_Agent.utils.error_position_mapper import AdvancedPositionMapper, SmartStatementResolver
from Conversion_Agent.utils.database_names import get_database_specific_terms
from Conversion_Agent.formatting.sql_splitter import split_sql_statements

# Local imports - Prompts
from Conversion_Agent.prompts.edge_case_enhanced_prompts import create_edge_case_validation_prompt
from Conversion_Agent.prompts.error_validation_prompt import create_error_validation_prompt
from Conversion_Agent.prompts.enhanced_source_mapping_prompt import create_error_statement_identification_prompt
from Conversion_Agent.prompts.position_based_validation_prompt import create_position_based_validation_prompt
from Conversion_Agent.prompts.source_mapping_validation_prompt import create_source_mapping_validation_prompt
from Conversion_Agent.prompts.statement_conversion_prompt import create_statement_conversion_prompt
from Conversion_Agent.prompts.src_tgt_validation_prompt import create_src_tgt_validation_prompt
from Conversion_Agent.prompts.two_phase_error_identification_prompt import create_phase1_error_identification_prompt, create_phase2_error_context_prompt

# Database connection import
from common.common import get_deployment_function
from common.database import target_statements_batch_insert
from common.database import connect_database, conversion_agent_deployment_insert


def get_conversion_path_from_state(state) -> str:
    """Get the conversion directory path from workflow state."""
    conversion_path = getattr(state, "conversion_path", "")
    if not conversion_path:
        raise ValueError("Conversion path not found in workflow state. Make sure setup_conversion_directories() was called.")
    return conversion_path


def get_target_statement_excel_path(conversion_path: str, target_statement_number: int) -> str:
    """Get the path for the Excel file for a specific target statement."""
    filename = f"Migration_TargetStatement_{target_statement_number}.xlsx"
    return os.path.join(conversion_path, filename)


def get_target_statement_sql_path(conversion_path: str, target_statement_number: int) -> str:
    """Get the path for the SQL file for a specific target statement."""
    filename = f"Migration_TargetStatement_{target_statement_number}.sql"
    return os.path.join(conversion_path, filename)


def create_excel_with_sheet(file_path: str, sheet_name: str, data: List[Dict[str, Any]]) -> str:
    """
    Generic function to create new Excel file with first sheet.

    Args:
        file_path: Full path to Excel file
        sheet_name: Name of the sheet to create
        data: List of dictionaries containing sheet data

    Returns:
        str: Path to the created Excel file
    """
    try:
        df = pd.DataFrame(data)
        with pd.ExcelWriter(file_path, mode='w', engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name=sheet_name, index=False)
        return file_path
    except Exception as e:
        print(f"❌ Error creating Excel file {file_path}: {e}")
        return ""


def append_sheet_to_excel(file_path: str, sheet_name: str, data: List[Dict[str, Any]]) -> bool:
    """
    Generic function to append data to existing Excel sheet without reading full content.

    Args:
        file_path: Full path to Excel file
        sheet_name: Name of the sheet to append
        data: List of dictionaries containing sheet data

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        df = pd.DataFrame(data)

        if not os.path.exists(file_path):
            # Create new file
            with pd.ExcelWriter(file_path, mode='w', engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name=sheet_name, index=False)
        else:
            # Append to existing file - add rows to the bottom of existing sheet
            with pd.ExcelWriter(file_path, mode='a', if_sheet_exists='overlay', engine='openpyxl') as writer:
                # Check if sheet exists to determine start row
                if sheet_name in writer.book.sheetnames:
                    existing_sheet = writer.book[sheet_name]
                    start_row = existing_sheet.max_row  # Start after last row
                    df.to_excel(writer, sheet_name=sheet_name, index=False, header=False, startrow=start_row)
                else:
                    # Sheet doesn't exist, create new with headers
                    df.to_excel(writer, sheet_name=sheet_name, index=False)

        return True
    except Exception as e:
        print(f"❌ Error appending sheet {sheet_name} to {file_path}: {e}")
        return False


def save_to_target_excel(conversion_path: str, target_statement_number: int, attempt_number: int,
                        sheet_name: str, data: List[Dict[str, Any]], description: str = "data") -> None:
    """
    Generic function to save data to target statement Excel file (creates file if needed).

    Args:
        conversion_path: Conversion directory path from workflow state
        target_statement_number: Target statement number
        attempt_number: Current attempt number
        sheet_name: Name of the sheet to append to
        data: Data to append
        description: Description for logging
    """
    excel_path = get_target_statement_excel_path(conversion_path, target_statement_number)

    try:
        # Add attempt number to all data rows
        for row in data:
            row["Attempt_Number"] = attempt_number

        # Use generic function to append sheet (handles file creation automatically)
        success = append_sheet_to_excel(excel_path, sheet_name, data)

        if success:
            print(f"📝 Saved {description} to {sheet_name} sheet for Target Statement {target_statement_number}, Attempt {attempt_number}")

    except Exception as e:
        print(f"❌ Error saving {description} to Excel file: {str(e)}")


def append_to_target_statement_excel(conversion_path: str, target_statement_number: int, attempt_number: int,
                                   sheet_name: str, data: List[Dict[str, Any]]) -> None:
    """Legacy wrapper for backward compatibility."""
    save_to_target_excel(conversion_path, target_statement_number, attempt_number, sheet_name, data, "data")


def save_original_target_code(conversion_path: str, target_code: str) -> str:
    """Save the original target code at workflow start."""
    file_path = os.path.join(conversion_path, "original_target_code.sql")
    return save_sql_file(file_path, target_code, "original target code")


def save_sql_file(file_path: str, sql_content: str, description: str = "SQL") -> str:
    """Generic function to save SQL content to file."""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(sql_content)

        print(f"💾 Saved {description}: {os.path.abspath(file_path)}")
        return file_path

    except Exception as e:
        print(f"❌ Error saving {description}: {str(e)}")
        return ""


def save_target_statement_sql_file(conversion_path: str, target_statement_number: int, sql_content: str) -> str:
    """Save final SQL content for a target statement."""
    sql_path = get_target_statement_sql_path(conversion_path, target_statement_number)
    return save_sql_file(sql_path, sql_content, f"SQL for Target Statement {target_statement_number}")


def create_statements_excel_file(conversion_path: str, source_statements: List[str], target_statements: List[str]) -> str:
    """Create statements.xlsx file with both source and target statements using generic functions."""
    try:
        excel_path = os.path.join(conversion_path, "statements.xlsx")

        # Create source statements data
        source_data = [
            {"Statement Number": i + 1, "Statement": stmt}
            for i, stmt in enumerate(source_statements)
        ]

        # Create target statements data
        target_data = [
            {"Statement Number": i + 1, "Statement": stmt}
            for i, stmt in enumerate(target_statements)
        ]

        # Create Excel file with Source_Statements sheet first
        create_excel_with_sheet(excel_path, "Source_Statements", source_data)

        # Append Target_Statements sheet
        append_sheet_to_excel(excel_path, "Target_Statements", target_data)

        print(f"📁 Created statements.xlsx with {len(source_statements)} source and {len(target_statements)} target statements")
        return excel_path

    except Exception as e:
        print(f"❌ Error creating statements.xlsx: {str(e)}")
        return ""



class UniversalCodeMigrationNodes:
    """
    Comprehensive database migration workflow nodes with AI-driven analysis.

    This class provides a complete suite of specialized nodes for database migration workflows,
    implementing sophisticated AI-driven analysis, validation, and conversion capabilities.
    Each node handles a specific aspect of the migration process with comprehensive error
    handling, validation loops, and audit trail generation.

    Key Capabilities:
        - Hybrid AI/position-based error identification
        - Intelligent source-to-target statement mapping
        - Context-aware SQL conversion with validation
        - Real database deployment testing
        - Iterative improvement through feedback loops
        - Comprehensive audit trail generation

    Workflow Integration:
        Designed for use with LangGraph workflow orchestration, providing seamless
        state management and conditional routing based on validation results.
    """

    def __init__(self, llm):
        """
        Initialize the migration nodes with AI language model integration.

        Sets up the node collection with the provided language model for AI-driven
        analysis throughout the database migration workflow.

        Args:
            llm: Initialized Language Model instance supporting structured outputs
                 for reliable AI analysis and conversion operations. Compatible with
                 multiple providers (OpenAI, Azure OpenAI, Anthropic, Groq, Gemini, Ollama).

        Attributes:
            llm: The language model instance used across all migration nodes
        """
        self.llm = llm

    def update_attempt_history(self, attempt_history: Dict[int, List[Dict]], target_statement_number: int, attempt_data: Dict) -> Dict[int, List[Dict]]:
        """Update attempt history keeping only last 10 records per statement."""
        if target_statement_number not in attempt_history:
            attempt_history[target_statement_number] = []

        # Add new attempt
        attempt_history[target_statement_number].append(attempt_data)

        # Keep only last 10 records for this statement
        if len(attempt_history[target_statement_number]) > 10:
            attempt_history[target_statement_number] = attempt_history[target_statement_number][-10:]

        return attempt_history

    def splitStatments(self, state: WorkflowState) -> Dict[str, Any]:
        """
        Split source and target code into individual SQL statements and insert into database.

        Args:
            state: Workflow state containing source and target code

        Returns:
            Dict with split statements, position mapper, and iteration count
        """
        try:
            print("🔄 Starting statement splitting...")

            source_code = state.source_code
            target_code = state.target_code
            target_object_id = getattr(state, 'target_object_id', None)
            # Get conversion path from workflow state
            conversion_path = get_conversion_path_from_state(state)

            # Save original target code at workflow start
            save_original_target_code(conversion_path, target_code)

            # Split statements
            source_statements = split_sql_statements(source_code)
            target_position_mapper = AdvancedPositionMapper()
            target_statements, target_position_mapper = target_position_mapper.split_with_comprehensive_mapping(target_code)

            # Create statements.xlsx file with both source and target statements
            create_statements_excel_file(conversion_path, source_statements, target_statements)

            print(f"✅ Split into {len(source_statements)} source and {len(target_statements)} target statements")
            # Store original target statements (never to be modified)
            original_target_statements = target_statements.copy()

            # Batch insert original target statements into database
            if target_object_id and original_target_statements:
                try:
                    print("🔄 Starting batch insert of original target statements...")

                    # Prepare data for batch insert using original target statements
                    statements_data = []
                    for i, statement in enumerate(original_target_statements, 1):
                        statements_data.append((i, statement))
                    if statements_data:
                        # Get database connection
                        connection = connect_database(state.project_db_credentials)
                        # Call batch insert function
                        success = target_statements_batch_insert(connection, target_object_id, statements_data)

                        if success:
                            print(f"✅ Successfully inserted {len(statements_data)} original target statements into database")
                        else:
                            print("❌ Failed to insert original target statements into database")
                    else:
                        print("⚠️ No valid original statements to insert")

                except Exception as db_error:
                    print(f"❌ Database insert error: {str(db_error)}")
                    # Continue with workflow even if database insert fails
            else:
                print("⚠️ Skipping database insert - tgt_id not provided or no original target statements")

            return {
                "source_statements": source_statements,
                "target_statements": target_statements,
                "original_target_statements": original_target_statements,
                "attempt_history": {},  # Initialize empty attempt history dict {statement_number: [last_10_attempts]}
                "target_position_mapper": target_position_mapper.to_dict() if target_position_mapper else None,
                "iteration_count": 1
            }
        except Exception as e:
            print(f"❌ Error in statement splitting: {str(e)}")
            return {
                "source_statements": [],
                "target_statements": [],
                "target_position_mapper": None,
                "iteration_count": 1
            }

    def AnalyzeError_identifyTargetStatements(self, state: WorkflowState) -> Dict[str, Any]:
        """
        Identify problematic target statement using position-based resolution with AI fallback.

        Args:
            state: Workflow state with target statements and deployment error

        Returns:
            Dict containing target error context (before/error/after statements)
        """
        try:
            print("🔍 Identifying error statement...")

            # Print deployment error for debugging
            deployment_error = state.deployment_error
            print(f"📋 DEPLOYMENT ERROR: {deployment_error}")

            target_statements = state.target_statements or []
            previous_feedback = getattr(state, 'error_identification_feedback', None)

            if not target_statements or not deployment_error:
                print("⚠️ Missing required data")
                return {}

            # Try position-based resolution first
            target_mapper_dict = getattr(state, 'target_position_mapper', None)
            print(f"🗺️ Target position mapper available: {target_mapper_dict is not None}")

            target_position_mapper = AdvancedPositionMapper.from_dict(target_mapper_dict) if target_mapper_dict else None
            print(f"🗺️ Position mapper created: {target_position_mapper is not None}")

            iteration_count = getattr(state, 'iteration_count', 1)

            target_error_context = None
            if target_position_mapper:
                print("🎯 Attempting position-based resolution...")
                resolver = SmartStatementResolver(target_position_mapper)
                statement_num = resolver.resolve_statement_by_position(deployment_error, iteration_count)
                print(f"🎯 Position resolution result: {statement_num}")

                if statement_num:
                    print(f"🎯 Position-based resolution: Statement #{statement_num}")
                    position_based_context = self.create_error_context_around_statement(target_statements, statement_num)

                    # Validate with AI
                    validation_result = self.validate_position_based_identification(position_based_context, deployment_error)

                    if validation_result['is_correct']:
                        print(f"✅ Position-based identification validated")
                        target_error_context = position_based_context
                    else:
                        print(f"❌ Position-based validation failed")
                else:
                    print("❌ Position-based resolution returned None")

            # Fallback to AI approach if position-based failed
            if not target_error_context:
                print("🧠 Using AI-based identification...")
                target_error_context = self.findErrorStatementWithTwoPhaseAI(target_statements, deployment_error, previous_feedback)

            if not target_error_context:
                print("❌ Could not identify error statement")
                return {}

            print(f"✅ Identified error statement: #{target_error_context.error_statement_number}")

            return {
                "target_error_context": target_error_context
            }

        except Exception as e:
            print(f"❌ Error in identification: {str(e)}")
            return {}

    def create_error_context_around_statement(self, statements: List[str], statement_num: int) -> ErrorContext:
        """Create error context (before/error/after) around a specific statement."""
        try:
            # Calculate before and after statement numbers
            before_stmt_num = statement_num - 1 if statement_num > 1 else 0
            after_stmt_num = statement_num + 1 if statement_num < len(statements) else 0

            # Get statement texts
            before_stmt = statements[before_stmt_num - 1] if before_stmt_num > 0 else ""
            error_stmt = statements[statement_num - 1] if 1 <= statement_num <= len(statements) else ""
            after_stmt = statements[after_stmt_num - 1] if after_stmt_num > 0 else ""

            return ErrorContext(
                before_statement=before_stmt,
                before_statement_number=before_stmt_num,
                error_statement=error_stmt,
                error_statement_number=statement_num,
                after_statement=after_stmt,
                after_statement_number=after_stmt_num
            )
        except Exception as e:
            print(f"❌ Error creating error context: {str(e)}")
            return None

    def validate_position_based_identification(self, target_error_context: ErrorContext, deployment_error: str) -> Dict[str, Any]:
        """Validate position-based error identification using AI."""
        try:
            prompt = create_position_based_validation_prompt(target_error_context, deployment_error)
            structured_llm = self.llm.client.with_structured_output(ValidationOutput)
            validation_result = structured_llm.invoke(prompt)

            return {
                'is_correct': validation_result.is_correct,
                'confidence': validation_result.confidence,
                'explanation': validation_result.explanation
            }

        except Exception as e:
            print(f"❌ Validation error: {str(e)}")
            return {
                'is_correct': False,
                'confidence': 0.0,
                'explanation': f"Validation error: {str(e)}"
            }

    def findErrorStatementWithTwoPhaseAI(self, target_statements: List[str], error_message: str, previous_feedback: str = None) -> ErrorContext:
        """Use two-phase AI approach to identify the specific statement causing the deployment error."""
        print(f"🧠 Phase 1: Identifying error statement...")

        total_statements = len(target_statements)
        print(f"📊 Analyzing {total_statements} statements")

        if previous_feedback:
            print(f"📝 Using previous feedback: {previous_feedback}")

        # Phase 1: Identify error statement
        phase1_prompt = create_phase1_error_identification_prompt(target_statements, error_message, previous_feedback)
        structured_llm = self.llm.client.with_structured_output(Phase1ErrorIdentificationOutput)
        phase1_result = structured_llm.invoke(phase1_prompt)

        identified_error_statement = phase1_result.error_statement_number
        confidence_score = phase1_result.confidence_score
        reasoning = phase1_result.reasoning

        print(f"🎯 Phase 1 identified statement #{identified_error_statement} (confidence: {confidence_score:.2f})")
        print(f"📝 Reasoning: {reasoning}")

        if identified_error_statement < 1 or identified_error_statement > len(target_statements):
            raise ValueError(f"Invalid error statement number: {identified_error_statement}")



        # Phase 2: Create error context
        print(f"🧠 Phase 2: Creating context around statement #{identified_error_statement}...")

        phase2_prompt = create_phase2_error_context_prompt(target_statements, identified_error_statement, error_message)
        structured_llm2 = self.llm.client.with_structured_output(Phase2ErrorContextOutput)
        phase2_result = structured_llm2.invoke(phase2_prompt)

        target_stmts = phase2_result.target_statements
        validation_notes = phase2_result.validation_notes

        print(f"🎯 Phase 2 completed with {len(target_stmts)} context statements")
        if validation_notes:
            print(f"📝 Notes: {validation_notes}")

        # Extract the context statements
        before_stmt_num = 0
        error_stmt_num = identified_error_statement
        after_stmt_num = 0

        for stmt in target_stmts:
            if stmt.statement_type == "before_error":
                before_stmt_num = stmt.target_statement_number
            elif stmt.statement_type == "error_statement":
                error_stmt_num = stmt.target_statement_number
            elif stmt.statement_type == "after_error":
                after_stmt_num = stmt.target_statement_number

        # Validate statement numbers
        if error_stmt_num != identified_error_statement:
            raise ValueError(f"Phase 2 error statement mismatch: expected {identified_error_statement}, got {error_stmt_num}")

        context_count = sum(1 for num in [before_stmt_num, error_stmt_num, after_stmt_num] if num > 0)
        print(f"📊 Context: {context_count} statements (Before: {before_stmt_num}, Error: {error_stmt_num}, After: {after_stmt_num})")

        # Get statement texts
        before_stmt = target_statements[before_stmt_num-1] if before_stmt_num > 0 and before_stmt_num <= len(target_statements) else ""
        error_stmt = target_statements[error_stmt_num-1] if error_stmt_num > 0 and error_stmt_num <= len(target_statements) else ""
        after_stmt = target_statements[after_stmt_num-1] if after_stmt_num > 0 and after_stmt_num <= len(target_statements) else ""

        # Create and return ErrorContext
        return ErrorContext(
            before_statement=before_stmt,
            before_statement_number=before_stmt_num,
            error_statement=error_stmt,
            error_statement_number=error_stmt_num,
            after_statement=after_stmt,
            after_statement_number=after_stmt_num
        )
    
    def save_error_context_to_target_excel(self, conversion_path: str, error_context: ErrorContext, target_statement_number: int, current_attempt: int):
        """Save error context to target statement Excel file."""
        try:
            # Get dynamic database names
            db_terms = get_database_specific_terms()
            target_db = db_terms['target_db']

            # Create data for DataFrame with enhanced information
            data = []

            # Before statement
            if error_context.before_statement_number > 0:
                data.append({
                    "Statement Number": error_context.before_statement_number,
                    "Statement": error_context.before_statement,
                    "Type": "Before Error",
                    "Database Specific": f"{target_db} Target"
                })

            # Error statement
            if error_context.error_statement_number > 0:
                data.append({
                    "Statement Number": error_context.error_statement_number,
                    "Statement": error_context.error_statement,
                    "Type": "ERROR STATEMENT",
                    "Database Specific": f"{target_db} Target"
                })

            # After statement
            if error_context.after_statement_number > 0:
                data.append({
                    "Statement Number": error_context.after_statement_number,
                    "Statement": error_context.after_statement,
                    "Type": "After Error",
                    "Database Specific": f"{target_db} Target"
                })

            # Save to target statement Excel file
            append_to_target_statement_excel(conversion_path, target_statement_number, current_attempt, "Error_Context", data)

        except Exception as e:
            print(f"⚠️ Could not save error context to target Excel: {e}")

    def save_error_validation_to_target_excel(self, conversion_path: str, target_error_context: ErrorContext, error_message: str,
                                             is_correct: bool, explanation: str, target_statement_number: int, current_attempt: int):
        """Save error validation results to target statement Excel file."""
        try:
            # Create data for DataFrame
            data = [
                {
                    "Validation Result": "PASSED" if is_correct else "FAILED",
                    "Explanation": explanation,
                    "Error Message": error_message,
                    "Target Before Statement Number": target_error_context.before_statement_number,
                    "Target Before Statement": target_error_context.before_statement,
                    "Target Error Statement Number": target_error_context.error_statement_number,
                    "Target Error Statement": target_error_context.error_statement,
                    "Target After Statement Number": target_error_context.after_statement_number,
                    "Target After Statement": target_error_context.after_statement,
                    "Timestamp": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")
                }
            ]

            # Save to target statement Excel file
            append_to_target_statement_excel(conversion_path, target_statement_number, current_attempt, "Error_Validation", data)

        except Exception as e:
            print(f"⚠️ Could not save error validation to target Excel: {e}")

    def validate_error_identification(self, state: WorkflowState) -> Dict[str, Any]:
        """Validate the error identification using AI."""
        try:
            print("🔍 Starting error identification validation...")

            # Get the target error context
            target_error_context = state.target_error_context
            deployment_error = state.deployment_error

            # Get current validation attempts count
            validation_attempts = getattr(state, 'validation_attempts', 0) + 1
            print(f"📊 Error identification validation attempt {validation_attempts}")

            if not target_error_context:
                print("⚠️ No target error context to validate")
                return {
                    "validation_successful": False,
                    "validation_attempts": validation_attempts
                }

            print(f"📊 Validating error identification for statement #{target_error_context.error_statement_number}")

            # Use AI to validate the error identification using just the context statements
            validation_result = self.validateErrorIdentificationWithContext(
                target_error_context,
                deployment_error
            )

            # Extract validation success and explanation
            validation_successful = validation_result.get('is_correct', False)
            explanation = validation_result.get('explanation', 'No explanation provided')
            confidence = validation_result.get('confidence', 0.0)

            # Note: Error validation results will be saved to target Excel file after validation success

            if validation_successful:
                print(f"✅ Error identification validated successfully with confidence {confidence}")

                # IMPORTANT: Capture target statement number ONLY after successful validation
                current_target_statement_number = target_error_context.error_statement_number
                previous_target_statement_number = getattr(state, 'target_statement_number', None)
                max_attempt_per_statement = getattr(state, 'max_attempt_per_statement', 5)

                print(f"🎯 Target statement number validated: {current_target_statement_number}")

                # Determine if this is a new target statement or continuation of previous
                if previous_target_statement_number is None:
                    # First target statement in the workflow
                    current_attempt = 1
                    print(f"🆕 First target statement in workflow: Statement #{current_target_statement_number}")
                elif current_target_statement_number == previous_target_statement_number:
                    # Same target statement - increment attempt
                    current_attempt = getattr(state, 'current_attempt_for_statement', 1) + 1
                    print(f"🔄 Same target statement #{current_target_statement_number}, Attempt #{current_attempt}")
                else:
                    # Different target statement identified - IMMEDIATELY switch to new statement
                    current_attempt = 1
                    print(f"🎯 NEW target statement #{current_target_statement_number} identified (was #{previous_target_statement_number})")
                    print(f"🔄 Switching to new statement, resetting to Attempt #1")
                    print(f"📊 Reason: Error analysis found the real error is in statement #{current_target_statement_number}")

                # Create or append to target statement Excel file now that we have validated target statement
                # Source and target statements are now in statements.xlsx, not in target statement files
                # source_statements = getattr(state, 'source_statements', [])
                # target_statements = getattr(state, 'target_statements', [])
                target_error_context = getattr(state, 'target_error_context', None)

                conversion_path = get_conversion_path_from_state(state)
                excel_path = get_target_statement_excel_path(conversion_path, current_target_statement_number)

                if current_attempt == 1 or not os.path.exists(excel_path):
                    # Create new Excel file with Error_Context sheet using existing function (Source & Target statements now in statements.xlsx)
                    if target_error_context:
                        self.save_error_context_to_target_excel(conversion_path, target_error_context, current_target_statement_number, current_attempt)
                        print(f"📁 Created Excel file for Target Statement #{current_target_statement_number} with Error_Context sheet")

                        # Add Error_Validation sheet
                        deployment_error = getattr(state, 'deployment_error', '')
                        self.save_error_validation_to_target_excel(
                            conversion_path, target_error_context, deployment_error, validation_successful, explanation,
                            current_target_statement_number, current_attempt
                        )
                        print(f"📝 Added Error_Validation sheet to Target Statement #{current_target_statement_number}")
                    else:
                        # Fallback: create empty file if no error context
                        excel_path = get_target_statement_excel_path(conversion_path, current_target_statement_number)
                        create_excel_with_sheet(excel_path, "Placeholder", [{"Info": "No error context available"}])
                        print(f"📁 Created empty Excel file for Target Statement #{current_target_statement_number}")

                else:
                    # Append source and target statements for this attempt (now in statements.xlsx)
                    # source_data = [
                    #     {"Statement Number": i+1, "Statement": stmt}
                    #     for i, stmt in enumerate(source_statements)
                    # ]
                    # target_data = [
                    #     {"Statement Number": i+1, "Statement": stmt}
                    #     for i, stmt in enumerate(target_statements)
                    # ]

                    # append_to_target_statement_excel(current_target_statement_number, current_attempt, "Source_Statements", source_data)
                    # append_to_target_statement_excel(current_target_statement_number, current_attempt, "Target_Statements", target_data)

                    # Add Error_Context sheet for this attempt
                    if target_error_context:
                        self.save_error_context_to_target_excel(conversion_path, target_error_context, current_target_statement_number, current_attempt)

                    # Add Error_Validation sheet for this attempt
                    deployment_error = getattr(state, 'deployment_error', '')
                    self.save_error_validation_to_target_excel(
                        conversion_path, target_error_context, deployment_error, validation_successful, explanation,
                        current_target_statement_number, current_attempt
                    )

                    print(f"📝 Appended all sheets for Target Statement #{current_target_statement_number}, Attempt #{current_attempt}")

                # Get attempt history (per statement, last 10 records)
                attempt_history = getattr(state, 'attempt_history', {})

                # Add current attempt to history (last 10 records per statement)
                current_attempt_data = {
                    "attempt_number": current_attempt,
                    "timestamp": pd.Timestamp.now().isoformat(),
                    "phase": "error_identification_validation",
                    "validation_result": "PASSED",
                    "explanation": explanation,
                    "error_statement_number": current_target_statement_number,
                    "deployment_error": getattr(state, 'deployment_error', 'N/A')
                }

                print(f"📝 ATTEMPT HISTORY: Statement #{current_target_statement_number}, Attempt #{current_attempt} - Error Identification Validation: PASSED")

                # Update attempt history keeping last 10 records for this statement
                attempt_history = self.update_attempt_history(attempt_history, current_target_statement_number, current_attempt_data)

                return {
                    "validation_successful": True,
                    "validation_attempts": validation_attempts,
                    "error_identification_feedback": None,  # Clear feedback on success
                    "target_statement_number": current_target_statement_number,
                    "previous_target_statement_number": previous_target_statement_number,
                    "current_attempt_for_statement": current_attempt,
                    "max_attempt_per_statement": max_attempt_per_statement,
                    "attempt_history": attempt_history  # Pass updated history (last 10 per statement)
                }
            else:
                print(f"❌ Error identification validation failed with confidence {confidence}")
                print(f"📝 Validation feedback: {explanation}")

                # Return the result along with the updated attempts count and feedback
                return {
                    "validation_successful": False,
                    "validation_attempts": validation_attempts,
                    "error_identification_feedback": explanation  # Store failure feedback
                }

        except Exception as e:
            print(f"❌ Error in validation: {str(e)}")
            # Get current validation attempts count even in case of error
            validation_attempts = getattr(state, 'validation_attempts', 0) + 1
            return {
                "validation_successful": False,
                "validation_attempts": validation_attempts,
                "error_identification_feedback": f"Validation error: {str(e)}"  # Store error as feedback
            }


    def validateErrorIdentificationWithContext(self, target_error_context: ErrorContext, error_message: str) -> Dict[str, Any]:
        """Use AI to validate if the identified error statement is correct using the error context with edge case handling."""
        print(f"🧠 Using AI to validate error identification...")

        # Detect edge cases based on available context
        available_count = sum(1 for num in [target_error_context.before_statement_number,
                                          target_error_context.error_statement_number,
                                          target_error_context.after_statement_number] if num > 0)

        is_edge_case = available_count <= 2

        if is_edge_case:
            print(f"⚠️ Edge case detected: Only {available_count} statement(s) available for validation")

            # Determine edge case type
            context_type = "single" if available_count == 1 else "minimal"
            if target_error_context.error_statement_number == 1:
                context_type = "first_statement"
            elif target_error_context.after_statement_number == 0 and target_error_context.before_statement_number > 0:
                context_type = "last_statement"

            # Use edge case enhanced prompt with error message
            prompt = create_edge_case_validation_prompt(target_error_context, error_message, context_type)
            print(f"📍 Using edge case validation strategy: {context_type}")
        else:
            # Use standard validation prompt with error message
            prompt = create_error_validation_prompt(target_error_context, error_message)
            print("📍 Using standard validation strategy")

        # Use structured output for reliable parsing
        structured_llm = self.llm.client.with_structured_output(ValidationOutput)
        ai_result = structured_llm.invoke(prompt)

        is_correct = ai_result.is_correct
        confidence = ai_result.confidence
        explanation = ai_result.explanation

        # Create result
        result = {
            'is_correct': is_correct,
            'explanation': explanation,
            'confidence': confidence,
            'edge_case_handled': is_edge_case,
            'context_type': context_type if is_edge_case else "standard"
        }

        print(f"🎯 AI validation result: correct={is_correct}, confidence={confidence}")
        if is_edge_case:
            print(f"📍 Edge case handling: {context_type}")
        print(f"📝 Explanation: {explanation}")

        return result

    def mapSource_withTargetStatements(self, state: WorkflowState) -> Dict[str, Any]:
        """Map target error context to corresponding source statements using hybrid approach."""
        try:
            print("🔍 Starting source statement mapping...")

            # Get the source statements and target error context
            source_statements = state.source_statements or []
            target_error_context = state.target_error_context
            previous_feedback = getattr(state, 'source_mapping_feedback', None)

            if not source_statements:
                print("⚠️ No source statements to analyze")
                return {}

            if not target_error_context:
                print("⚠️ No target error context provided")
                return {}

            if previous_feedback:
                print(f"� Previous mapping feedback: {previous_feedback}")

            # Use AI-based source mapping (position-based mapping removed due to complexity)
            print(f"🧠 Using AI-based source mapping...")
            print(f"📊 Source statements: {len(source_statements)}, Target error statement: #{target_error_context.error_statement_number}")

            # Extract target context for AI mapping
            target_context = [
                (target_error_context.before_statement_number, target_error_context.before_statement),
                (target_error_context.error_statement_number, target_error_context.error_statement),
                (target_error_context.after_statement_number, target_error_context.after_statement)
            ]
            target_context = [(num, stmt) for num, stmt in target_context if num > 0 and stmt]

            source_context = self.findSourceStatementsWithAI(source_statements, target_context, target_error_context, previous_feedback)

            if not source_context:
                print("❌ All source mapping approaches failed")
                return {}

            # Print final mapping results
            print(f"📊 Source mapping completed using: AI-based mapping")
            print(f"   Before=#{source_context.before_statement_number}, Error=#{source_context.error_statement_number}, After=#{source_context.after_statement_number}")

            # Save source context to Excel
            self.saveSourceContextToExcel(source_context, state)

            print(f"✅ Source mapping completed successfully")

            return {
                "source_context": source_context
            }

        except Exception as e:
            print(f"❌ Error in source statement mapping: {str(e)}")
            return {}

    def findSourceStatementsWithAI(self, source_statements: List[str], target_context: List[Tuple[int, str]], target_error_context: ErrorContext, previous_feedback: str = None) -> ErrorContext:
        """Enhanced AI-based source mapping with comprehensive edge case handling"""
        try:
            print(f"🧠 Using AI-based source mapping with edge case handling...")

            # Extract target error statement for analysis
            target_error_num = target_error_context.error_statement_number
            target_error_stmt = target_error_context.error_statement

            print(f"🔍 Analyzing target context with {len(target_context)} statements for source mapping")

            # EDGE CASE 1: Limited context scenarios
            if len(target_context) == 1:
                print("⚠️ Edge case: Only error statement available for source mapping - limited context")
            elif len(target_context) == 2:
                print("⚠️ Edge case: Only 2 statements available for source mapping - minimal context")
            elif len(target_context) == 3:
                print(f"📍 Full context available: Statement #{target_error_num} (error statement) + before/after context")
            else:
                print(f"📍 {len(target_context)} statement context: Statement #{target_error_num} (error statement)")

            # EDGE CASE 2: Missing context statements
            missing_context = []
            if target_error_context.before_statement_number == 0:
                missing_context.append("before")
            if target_error_context.after_statement_number == 0:
                missing_context.append("after")
            if missing_context:
                print(f"📍 Missing context: {', '.join(missing_context)} statement(s) not available")

            # EDGE CASE 3: Statement count mismatch analysis
            if target_error_num > len(source_statements):
                print(f"⚠️ Edge case: Target error statement #{target_error_num} exceeds source statements ({len(source_statements)})")
                print(f"📍 This suggests significant structural differences between source and target")

            print(f"🔍 AI analyzing target error statement #{target_error_num}")
            print(f"📝 Target statement: {target_error_stmt[:100]}...")

            if previous_feedback:
                print(f"📝 Incorporating previous mapping feedback: {previous_feedback}")

            # Use dedicated prompt function with structured output
            context_info = {
                'source_count': len(source_statements),
                'context_count': len(target_context),
                'missing_context': ', '.join(missing_context) if missing_context else 'None'
            }

            ai_prompt = create_error_statement_identification_prompt(
                source_statements=source_statements,
                target_error_statement=target_error_stmt,
                target_error_number=target_error_num,
                previous_feedback=previous_feedback,
                context_info=context_info
            )

            # Use structured output for reliable parsing
            structured_llm = self.llm.client.with_structured_output(Phase1IdentificationOutput)
            ai_result = structured_llm.invoke(ai_prompt)

            source_error_num = ai_result.source_statement_number
            confidence_score = ai_result.confidence_score
            reasoning = ai_result.reasoning

            print(f"🧠 AI reasoning: {reasoning}")
            print(f"🎯 Confidence score: {confidence_score}")

            if source_error_num is not None:

                # EDGE CASE 4: Target database-specific statement (no source equivalent)
                if source_error_num == 0:
                    # Get dynamic database names
                    db_terms = get_database_specific_terms()
                    source_db = db_terms['source_db']
                    target_db = db_terms['target_db']

                    print(f"🎯 {target_db}-specific statement detected - no {source_db} equivalent")
                    print(f"🎯 Creating special mapping for {target_db}-specific error statement")

                    # Create minimal context for target database-specific statements
                    source_context = ErrorContext(
                        error_statement="",  # No source equivalent
                        error_statement_number=0,
                        before_statement="",
                        before_statement_number=0,
                        after_statement="",
                        after_statement_number=0
                    )

                    print(f"🧠 AI mapping: Target #{target_error_num} → {target_db}-specific (no source)")
                    return source_context

                # EDGE CASE 5: Validate AI selection is within bounds
                elif 1 <= source_error_num <= len(source_statements):
                    print(f"🧠 AI selected source statement #{source_error_num}")

                    # Create context around the AI-selected statement with edge case handling
                    source_before_num = max(1, source_error_num - 1) if source_error_num > 1 else 0
                    source_after_num = min(len(source_statements), source_error_num + 1) if source_error_num < len(source_statements) else 0

                    # EDGE CASE 6: Handle boundary conditions
                    if source_error_num == 1:
                        print(f"📍 Edge case: Error statement is first source statement - no before context")
                    elif source_error_num == len(source_statements):
                        print(f"📍 Edge case: Error statement is last source statement - no after context")

                    source_context = ErrorContext(
                        error_statement=source_statements[source_error_num-1],
                        error_statement_number=source_error_num,
                        before_statement=source_statements[source_before_num-1] if source_before_num > 0 else "",
                        before_statement_number=source_before_num,
                        after_statement=source_statements[source_after_num-1] if source_after_num > 0 and source_after_num <= len(source_statements) else "",
                        after_statement_number=source_after_num if source_after_num <= len(source_statements) else 0
                    )

                    print(f"🧠 AI mapping: Target #{target_error_num} → Source #{source_error_num}")
                    return source_context

                else:
                    print(f"⚠️ Edge case: AI selected invalid source statement #{source_error_num} (out of range 1-{len(source_statements)})")

            print(f"⚠️ AI could not determine valid source mapping")
            return None

        except Exception as e:
            print(f"❌ AI source mapping failed: {str(e)}")
            print(f"🔧 Edge case: Exception during AI mapping - this may indicate data quality issues")
            return None

    def saveSourceContextToExcel(self, source_context: ErrorContext, state: WorkflowState = None):
        """Save source context to Excel file using generic function."""
        try:
            # Get dynamic database names
            db_terms = get_database_specific_terms()
            source_db = db_terms['source_db']
            target_db = db_terms['target_db']

            # Get target statement tracking info
            target_statement_number = getattr(state, 'target_statement_number', None) if state else None
            current_attempt = getattr(state, 'current_attempt_for_statement', 1) if state else 1

            # Create data for DataFrame with target database-specific indicators
            data = []

            # Before statement
            if source_context.before_statement_number > 0 or source_context.before_statement.strip():
                before_type = "Before Error"
                if source_context.before_statement_number == 0:
                    before_type = f"Before Error ({target_db}-Specific)"
                data.append({
                    "Statement Number": source_context.before_statement_number,
                    "Statement": source_context.before_statement,
                    "Type": before_type,
                    "Database Specific": f"{target_db}-Specific" if source_context.before_statement_number == 0 else f"{source_db} Source"
                })

            # Error statement
            if source_context.error_statement_number > 0 or source_context.error_statement.strip():
                error_type = "ERROR STATEMENT"
                if source_context.error_statement_number == 0:
                    error_type = f"ERROR STATEMENT ({target_db}-Specific)"
                data.append({
                    "Statement Number": source_context.error_statement_number,
                    "Statement": source_context.error_statement,
                    "Type": error_type,
                    "Database Specific": f"{target_db}-Specific" if source_context.error_statement_number == 0 else f"{source_db} Source"
                })

            # After statement
            if source_context.after_statement_number > 0 or source_context.after_statement.strip():
                after_type = "After Error"
                if source_context.after_statement_number == 0:
                    after_type = f"After Error ({target_db}-Specific)"
                data.append({
                    "Statement Number": source_context.after_statement_number,
                    "Statement": source_context.after_statement,
                    "Type": after_type,
                    "Database Specific": f"{target_db}-Specific" if source_context.after_statement_number == 0 else f"{source_db} Source"
                })

            # Get conversion path from state
            conversion_path = get_conversion_path_from_state(state) if state else ""

            # Use generic function to save data
            save_to_target_excel(conversion_path, target_statement_number, current_attempt, "Source_Context", data, "source context")

        except Exception as e:
            print(f"⚠️ Could not save source context: {e}")

    def validate_source_mapping(self, state: WorkflowState) -> Dict[str, Any]:
        """Validate the source mapping using AI."""
        try:
            print("🔍 Starting source mapping validation...")

            # Get the source context
            source_context = state.source_context
            target_error_context = state.target_error_context

            # Get current source mapping attempts count
            source_mapping_attempts = getattr(state, 'source_mapping_attempts', 0) + 1
            print(f"📊 Source mapping validation attempt {source_mapping_attempts}")



            if not source_context:
                print("⚠️ No source context to validate")
                return {
                    "source_mapping_successful": False,
                    "source_mapping_attempts": source_mapping_attempts
                }

            if not target_error_context:
                print("⚠️ No target error context to validate against")
                return {
                    "source_mapping_successful": False,
                    "source_mapping_attempts": source_mapping_attempts
                }

            print(f"📊 Validating source mapping for error statement #{target_error_context.error_statement_number}")

            # Use AI to validate the source mapping
            validation_result = self.validateSourceMappingWithAI(
                source_context,
                target_error_context
            )

            # Extract validation success and explanation
            source_mapping_successful = validation_result.get('is_correct', False)
            explanation = validation_result.get('explanation', 'No explanation provided')

            # Save validation results to Excel
            self.saveValidationResultsToExcel(
                source_context,
                target_error_context,
                source_mapping_successful,
                explanation,
                state
            )

            if source_mapping_successful:
                print(f"✅ Source mapping validated successfully")
                return {
                    "source_mapping_successful": True,
                    "source_mapping_attempts": source_mapping_attempts,
                    "source_mapping_feedback": None  # Clear feedback on success
                }
            else:
                print(f"❌ Source mapping validation failed")
                print(f"📝 Mapping feedback: {explanation}")

                # Return the result along with the updated attempts count and feedback
                return {
                    "source_mapping_successful": False,
                    "source_mapping_attempts": source_mapping_attempts,
                    "source_mapping_feedback": explanation  # Store failure feedback
                }

        except Exception as e:
            print(f"❌ Error in source mapping validation: {str(e)}")
            # Get current source mapping attempts count even in case of error
            source_mapping_attempts = getattr(state, 'source_mapping_attempts', 0) + 1
            return {
                "source_mapping_successful": False,
                "source_mapping_attempts": source_mapping_attempts,
                "source_mapping_feedback": f"Validation error: {str(e)}"  # Store error as feedback
            }

    def saveValidationResultsToExcel(self, source_context: ErrorContext, target_error_context: ErrorContext, is_correct: bool, explanation: str, state: WorkflowState = None):
        """Save validation results to Excel file using generic function."""
        try:
            # Get dynamic database names
            db_terms = get_database_specific_terms()
            source_db = db_terms['source_db']
            target_db = db_terms['target_db']

            # Get target statement tracking info
            target_statement_number = getattr(state, 'target_statement_number', None) if state else None
            current_attempt = getattr(state, 'current_attempt_for_statement', 1) if state else 1

            # Create data for DataFrame with target database-specific indicators
            data = [
                {
                    "Validation Result": "PASSED" if is_correct else "FAILED",
                    "Explanation": explanation,
                    "Target Before Statement Number": target_error_context.before_statement_number,
                    "Target Before Statement": target_error_context.before_statement,
                    "Target Error Statement Number": target_error_context.error_statement_number,
                    "Target Error Statement": target_error_context.error_statement,
                    "Target After Statement Number": target_error_context.after_statement_number,
                    "Target After Statement": target_error_context.after_statement,
                    "Source Before Statement Number": source_context.before_statement_number,
                    "Source Before Statement": source_context.before_statement,
                    "Source Before Type": f"{target_db}-Specific" if source_context.before_statement_number == 0 else f"{source_db} Source",
                    "Source Error Statement Number": source_context.error_statement_number,
                    "Source Error Statement": source_context.error_statement,
                    "Source Error Type": f"{target_db}-Specific" if source_context.error_statement_number == 0 else f"{source_db} Source",
                    "Source After Statement Number": source_context.after_statement_number,
                    "Source After Statement": source_context.after_statement,
                    "Source After Type": f"{target_db}-Specific" if source_context.after_statement_number == 0 else f"{source_db} Source",
                    "Timestamp": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")
                }
            ]

            # Get conversion path from state
            conversion_path = get_conversion_path_from_state(state) if state else ""

            # Use generic function to save data
            save_to_target_excel(conversion_path, target_statement_number, current_attempt, "Mapping_Validation", data, "mapping validation results")

        except Exception as e:
            print(f"⚠️ Could not save validation results: {e}")

    def validateSourceMappingWithAI(self, source_context: ErrorContext, target_error_context: ErrorContext) -> Dict[str, Any]:
        """Use AI to validate if the source mapping is correct with enhanced multi-layer validation."""
        print(f"🧠 Using enhanced AI validation...")

        # Use single comprehensive AI validation
        prompt = create_source_mapping_validation_prompt(source_context, target_error_context)

        # Use structured output for reliable parsing
        structured_llm = self.llm.client.with_structured_output(ValidationOutput)
        ai_result = structured_llm.invoke(prompt)

        result = {
            'is_correct': ai_result.is_correct,
            'explanation': ai_result.explanation,
            'confidence': ai_result.confidence
        }

        if result['is_correct']:
            print(f"✅ Enhanced validation passed")
        else:
            print(f"⚠️ Enhanced validation failed: {result.get('explanation', 'Unknown reason')}")

        return result

    def Convert_TargetStatement(self, state: WorkflowState) -> Dict[str, Any]:
        """Convert the error statement from source to target database format."""
        try:
            print("🔄 Starting statement conversion...")

            # Always work from original target statements - no fallback
            original_target_statements = getattr(state, 'original_target_statements', None)
            if not original_target_statements:
                raise ValueError("Original target statements not found - should always be available")

            # Get attempt history (per statement, last 5 records)
            attempt_history = getattr(state, 'attempt_history', {})
            current_target_statement_number = getattr(state, 'target_statement_number', None)

            # Get the source and target contexts
            source_context = state.source_context
            target_error_context = state.target_error_context
            deployment_error = state.deployment_error

            # Get feedback from previous validation attempt
            previous_feedback = getattr(state, 'conversion_feedback', None)

            if previous_feedback:
                print(f"🔄 Previous conversion feedback: {previous_feedback}")

            # Check if this is a target-specific optimization scenario
            is_target_specific_optimization = (source_context and
                                             source_context.error_statement_number == 0)

            if is_target_specific_optimization:
                print("🚀 OPTIMIZATION MODE: Target-specific statement conversion")
                print("💡 Applying PostgreSQL expertise directly (no source reference needed)")

            if not source_context:
                print("⚠️ No source context provided")
                return {}

            if not target_error_context:
                print("⚠️ No target error context provided")
                return {}

            if not deployment_error:
                print("⚠️ No deployment error provided")
                deployment_error = "ERROR: operator does not exist: integer = character varying\nHINT: No operator matches the given name and argument types. You might need to add explicit type casts."
                print(f"⚠️ Using default error message: {deployment_error}")

            print(f"📊 Converting error statement")

            # For conversion, preserve position information to help target exact error location
            # Don't clean the error message for conversion - AI needs position info
            # Use original target statements and pass attempt history for AI learning
            conversion_result = self.convertStatementWithAI(source_context, target_error_context, original_target_statements, deployment_error, previous_feedback, attempt_history, current_target_statement_number)

            if not conversion_result or not conversion_result.get("ai_corrections"):
                print("❌ Could not convert error statement")
                return {}

            # Extract AI corrections, explanation, and functional validation
            ai_corrections = conversion_result["ai_corrections"]
            explanation = conversion_result["explanation"]
            functional_validation = conversion_result.get("functional_validation", None)

            # Update attempt history with current conversion attempt (last 10 records per statement)
            conversion_attempt_data = {
                "attempt_number": getattr(state, 'current_attempt_for_statement', 1),
                "timestamp": pd.Timestamp.now().isoformat(),
                "phase": "statement_conversion",
                "corrections_made": len(ai_corrections),
                "explanation": explanation,
                "error_statement_number": current_target_statement_number,
                "deployment_error": getattr(state, 'deployment_error', 'N/A')
            }

            # Add functional validation to attempt history if available
            if functional_validation:
                conversion_attempt_data.update({
                    "business_logic_preserved": functional_validation.business_logic_preserved,
                    "data_transformations_equivalent": functional_validation.data_transformations_equivalent,
                    "conditional_logic_maintained": functional_validation.conditional_logic_maintained,
                    "potential_behavior_differences": functional_validation.potential_behavior_differences
                })

            print(f"📝 ATTEMPT HISTORY: Statement #{current_target_statement_number}, Attempt #{getattr(state, 'current_attempt_for_statement', 1)} - Statement Conversion: {len(ai_corrections)} corrections generated")

            # Update attempt history keeping last 10 records for this statement
            if current_target_statement_number:
                attempt_history = self.update_attempt_history(attempt_history, current_target_statement_number, conversion_attempt_data)

            print(f"✅ Error statement conversion completed - {len(ai_corrections)} corrections generated")
            print(f"📝 AI Explanation: {explanation}")

            return {
                "ai_corrections": ai_corrections,
                "conversion_explanation": explanation,
                "functional_validation": functional_validation,  # Include functional validation
                "target_specific_optimization": is_target_specific_optimization,  # Track optimization usage
                "attempt_history": attempt_history  # Pass updated history (last 10 per statement)
            }

        except Exception as e:
            print(f"❌ Error in statement conversion: {str(e)}")
            return {}

    def convertStatementWithAI(self, source_context: ErrorContext, target_error_context: ErrorContext, target_statements: List[str], error_message: str, previous_feedback: str = None, attempt_history: dict = None, current_statement_number: int = None) -> Dict[str, Any]:
        """Use AI to convert the error statement from source to target database format using structured output."""
        print(f"🧠 Using AI to convert error statement...")

        if previous_feedback:
            print(f"📝 Incorporating previous conversion feedback: {previous_feedback}")

        # Analyze structural dependencies for enhanced context
        try:
            from Conversion_Agent.utils.context_aware_splitter import enhance_conversion_context
            enhanced_context = enhance_conversion_context(
                target_error_context.before_statement,
                target_error_context.error_statement,
                target_error_context.after_statement
            )
            print(f"🔍 Structural Analysis: {enhanced_context}")
        except Exception as e:
            print(f"⚠️ Context analysis failed: {e}")
            enhanced_context = ""

        # Create prompt for AI using the imported function with feedback and attempt history
        prompt = create_statement_conversion_prompt(source_context, target_error_context, error_message, target_statements, previous_feedback, attempt_history, current_statement_number, enhanced_context)

        # Use structured output for reliable parsing
        structured_llm = self.llm.client.with_structured_output(StatementConversionOutput)
        ai_result = structured_llm.invoke(prompt)

        corrected_stmts = ai_result.corrected_statements
        explanation = ai_result.explanation
        functional_validation = ai_result.functional_validation

        print(f"🎯 AI provided {len(corrected_stmts)} corrected statements")
        print(f"📝 Explanation: {explanation}")
        print(f"🔍 Functional Validation:")
        print(f"   Business Logic Preserved: {functional_validation.business_logic_preserved}")
        print(f"   Data Transformations Equivalent: {functional_validation.data_transformations_equivalent}")
        print(f"   Conditional Logic Maintained: {functional_validation.conditional_logic_maintained}")
        if functional_validation.potential_behavior_differences != "none":
            print(f"   ⚠️ Potential Differences: {functional_validation.potential_behavior_differences}")

        if not corrected_stmts:
            raise ValueError("AI did not return any corrected statements")

        # Return the AI corrections without applying them to target statements
        # The actual replacement will happen in targetcode_deployment node
        print(f"📋 Returning {len(corrected_stmts)} AI-generated corrections for validation")

        return {
            "ai_corrections": corrected_stmts,
            "explanation": explanation,
            "functional_validation": functional_validation
        }

    def saveCorrectedStatementsToExcel(self, corrected_statements: List[str], target_error_context: ErrorContext, state: WorkflowState = None):
        """Save corrected statements to Excel file using generic function."""
        try:
            # Get target statement tracking info
            target_statement_number = getattr(state, 'target_statement_number', None) if state else None
            current_attempt = getattr(state, 'current_attempt_for_statement', 1) if state else 1

            # Create data for DataFrame
            data = [
                {"Statement Number": target_error_context.before_statement_number,
                 "Statement": corrected_statements[target_error_context.before_statement_number-1] if 1 <= target_error_context.before_statement_number <= len(corrected_statements) else "",
                 "Type": "Before Error"},
                {"Statement Number": target_error_context.error_statement_number,
                 "Statement": corrected_statements[target_error_context.error_statement_number-1] if 1 <= target_error_context.error_statement_number <= len(corrected_statements) else "",
                 "Type": "CORRECTED ERROR STATEMENT"},
                {"Statement Number": target_error_context.after_statement_number,
                 "Statement": corrected_statements[target_error_context.after_statement_number-1] if 1 <= target_error_context.after_statement_number <= len(corrected_statements) else "",
                 "Type": "After Error"}
            ]

            # Filter out empty statements
            data = [d for d in data if d["Statement Number"] > 0 and d["Statement"]]

            # Get conversion path from state
            conversion_path = get_conversion_path_from_state(state) if state else ""

            # Use generic function to save data
            save_to_target_excel(conversion_path, target_statement_number, current_attempt, "AI_Corrections", data, "corrected statements")

        except Exception as e:
            print(f"⚠️ Could not save corrected statements: {e}")

    def replaceTargetStatement(self, state: WorkflowState) -> Dict[str, Any]:
        """Pass through AI corrections without doing code replacement - replacement happens in targetcode_deployment."""
        try:
            print("🔄 Passing AI corrections to deployment...")

            # Get the AI corrections
            ai_corrections = getattr(state, 'ai_corrections', None)

            # Get current iteration count
            iteration_count = getattr(state, 'iteration_count', 1)
            print(f"📊 Passing AI corrections for iteration {iteration_count}")

            if not ai_corrections:
                print("⚠️ No AI corrections provided")
                return {
                    "iteration_count": iteration_count  # Preserve iteration count
                }

            print(f"📊 Passing {len(ai_corrections)} AI corrections to deployment")

            return {
                "ai_corrections": ai_corrections,
                "iteration_count": iteration_count  # Preserve iteration count
            }

        except Exception as e:
            print(f"❌ Error in passing corrected statements: {str(e)}")
            # Get current iteration count even in case of error
            iteration_count = getattr(state, 'iteration_count', 1)
            return {
                "iteration_count": iteration_count  # Preserve iteration count
            }


    def targetcode_deployment(self, state: WorkflowState) -> Dict[str, Any]:
        """Apply AI corrections to target code and deploy to target database."""
        try:
            # Get dynamic database names
            db_terms = get_database_specific_terms()
            target_db = db_terms['target_db']

            print(f"🚀 Starting code replacement and deployment to {target_db}...")

            # Get migration name for dynamic database connection
            migration_name = getattr(state, 'migration_name', 'Oracle_Postgres14')
            print(f"🔧 Using migration type: {migration_name}")

            # Get the appropriate database deployment function dynamically
            deployToTargetDatabase = get_deployment_function(migration_name)
            print(f"✅ Loaded database connection for {migration_name}")

            # Use original target statements for position mapping consistency
            original_target_statements = getattr(state, 'original_target_statements', None)
            if original_target_statements:
                print("🎯 Using original target statements for position mapping consistency")
                # Statement numbers remain consistent across attempts
                # Position mapper uses updated code but maps to original statement positions

            # Get AI corrections and target statements (use existing split statements)
            ai_corrections = getattr(state, 'ai_corrections', None)
            target_statements = getattr(state, 'target_statements', None)
            updated_target_code = getattr(state, 'updated_target_code', None)

            # Get current iteration count
            iteration_count = getattr(state, 'iteration_count', 1)
            print(f"📊 Deployment attempt for iteration {iteration_count}")

            # Update attempt history with deployment attempt (last 10 records per statement)
            attempt_history = getattr(state, 'attempt_history', {})
            current_target_statement_number = getattr(state, 'target_statement_number', None)

            # OPTIMIZATION: Use existing target statements and directly update the corrected statement
            if ai_corrections and target_statements:
                print("🔄 OPTIMIZATION: Applying AI corrections using existing target statements...")
                print(f"📊 Applying {len(ai_corrections)} AI corrections to existing {len(target_statements)} statements")

                # Get the error statement number and corrected statement
                target_error_context = getattr(state, 'target_error_context', None)
                error_stmt_num = target_error_context.error_statement_number if target_error_context else None

                if error_stmt_num:
                    # Find the corrected error statement directly
                    corrected_error_stmt = None
                    for correction in ai_corrections:
                        if correction.statement_number == error_stmt_num and correction.statement_type == "error_statement":
                            corrected_error_stmt = correction.corrected_statement
                            break

                    if corrected_error_stmt:
                        # Validate statement number bounds
                        if 1 <= error_stmt_num <= len(target_statements):
                            print(f"✏️ OPTIMIZATION: Directly updating statement #{error_stmt_num} in existing statements array")
                            # Direct statement replacement - no need to copy entire array
                            target_statements[error_stmt_num-1] = corrected_error_stmt
                            print(f"✅ Successfully updated error statement #{error_stmt_num}")
                        else:
                            print(f"⚠️ Invalid error statement number: {error_stmt_num} (valid range: 1-{len(target_statements)})")
                    else:
                        print(f"⚠️ No corrected statement found for error statement #{error_stmt_num}")
                        # Log available corrections for debugging
                        available_corrections = [f"#{c.statement_number}({c.statement_type})" for c in ai_corrections]
                        print(f"🔍 Available corrections: {available_corrections}")
                else:
                    print("⚠️ No error statement number available from target error context")

                # Join the updated statements to create the updated target code
                updated_target_code = "\n".join(target_statements)
                print(f"📝 Generated updated target code with {len(target_statements)} statements")

                # Rebuild position mapper for the corrected code
                print("🔄 Rebuilding target position mapper for corrected code...")
                target_position_mapper = AdvancedPositionMapper()
                _, target_position_mapper = target_position_mapper.split_with_comprehensive_mapping(updated_target_code)
                print("✅ Target position mapper updated for corrected statements")

                # Save the updated target code to a file
                target_statement_number = getattr(state, 'target_statement_number', None)
                conversion_path = get_conversion_path_from_state(state)
                save_target_statement_sql_file(conversion_path, target_statement_number, updated_target_code)

                # Save corrected statements to Excel for tracking
                target_error_context = getattr(state, 'target_error_context', None)
                if target_error_context:
                    self.saveCorrectedStatementsToExcel(target_statements, target_error_context, state)

                print(f"✅ AI corrections applied successfully")

                # Update attempt history with deployment attempt
                if current_target_statement_number:
                    deployment_attempt_data = {
                        "attempt_number": getattr(state, 'current_attempt_for_statement', 1),
                        "timestamp": pd.Timestamp.now().isoformat(),
                        "phase": "deployment",
                        "corrections_applied": len(ai_corrections),
                        "deployment_status": "pending",
                        "error_statement_number": current_target_statement_number
                    }

                    print(f"📝 ATTEMPT HISTORY: Statement #{current_target_statement_number}, Attempt #{getattr(state, 'current_attempt_for_statement', 1)} - Deployment: {len(ai_corrections)} corrections applied")

                    # Update attempt history keeping last 10 records for this statement
                    attempt_history = self.update_attempt_history(attempt_history, current_target_statement_number, deployment_attempt_data)

            if not updated_target_code:
                print("⚠️ No updated target code available for deployment")
                return {
                    "deployment_successful": False,
                    "iteration_count": iteration_count  # Preserve iteration count
                }

            print(f"📊 Deploying updated target code to {target_db}")

            # Get target database credentials from workflow state
            target_db_credentials = getattr(state, "target_db_credentials", None)

            if not target_db_credentials:
                error_message = "Target database credentials not found in workflow state"
                print(f"❌ {error_message}")
                return {
                    "deployment_successful": False,
                    "error_message": error_message
                }

            # Deploy to target database with dynamic credentials
            deployment_successful, error_message = deployToTargetDatabase(updated_target_code, target_db_credentials)

            # Save deployment results
            self.saveDeploymentResults(deployment_successful, error_message, state)

            if deployment_successful:
                print(f"✅ Code deployed successfully to {target_db}")
            else:
                print(f"❌ Code deployment to {target_db} failed: {error_message}")

            return {
                "deployment_successful": deployment_successful,
                "error_message": error_message if not deployment_successful else None,
                "updated_target_code": updated_target_code,  # Include updated code in return
                "target_statements": target_statements,  # Pass updated statements array
                "original_target_statements": original_target_statements,  # Preserve original
                "attempt_history": attempt_history,  # Pass updated history (last 10 per statement)
                "target_position_mapper": target_position_mapper.to_dict() if 'target_position_mapper' in locals() else getattr(state, 'target_position_mapper', None),  # Pass updated mapper
                "iteration_count": iteration_count  # Preserve iteration count
            }

        except Exception as e:
            print(f"❌ Error in code deployment: {str(e)}")
            # Get current iteration count even in case of error
            iteration_count = getattr(state, 'iteration_count', 1)
            return {
                "deployment_successful": False,
                "error_message": str(e),
                "target_statements": getattr(state, 'target_statements', []),  # Pass current statements even on error
                "original_target_statements": getattr(state, 'original_target_statements', []),  # Preserve original even on error
                "attempt_history": getattr(state, 'attempt_history', {}),  # Pass current history even on error
                "target_position_mapper": getattr(state, 'target_position_mapper', None),  # Pass current mapper even on error
                "iteration_count": iteration_count  # Preserve iteration count
            }







    def saveDeploymentResults(self, deployment_successful: bool, error_message: str = None, state: WorkflowState = None):
        """Save the deployment results to an Excel file using generic function."""
        try:
            # Get target statement tracking info
            target_statement_number = getattr(state, 'target_statement_number', None) if state else None
            current_attempt = getattr(state, 'current_attempt_for_statement', 1) if state else 1

            # Create data for DataFrame
            data = [
                {"Status": "Success" if deployment_successful else "Failed",
                 "Timestamp": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S"),
                 "Error Message": error_message if error_message else "N/A"}
            ]

            # Get conversion path from state
            conversion_path = get_conversion_path_from_state(state) if state else ""

            # Use generic function to save data
            save_to_target_excel(conversion_path, target_statement_number, current_attempt, "Deployment_Results", data, "deployment results")

        except Exception as e:
            print(f"⚠️ Could not save deployment results: {e}")

    def saveConversionValidationToExcel(self, source_context: ErrorContext, target_error_context: ErrorContext, corrected_error_stmt: str, is_correct: bool, explanation: str, state: WorkflowState = None, functional_validation=None):
        """Save conversion validation results to Excel file using generic function."""
        try:
            # Get dynamic database names
            db_terms = get_database_specific_terms()
            source_db = db_terms['source_db']
            target_db = db_terms['target_db']

            # Get target statement tracking info
            target_statement_number = getattr(state, 'target_statement_number', None) if state else None
            current_attempt = getattr(state, 'current_attempt_for_statement', 1) if state else 1

            # Create data for DataFrame with target database-specific indicators and functional validation
            data_row = {
                "Validation Result": "PASSED" if is_correct else "FAILED",
                "Explanation": explanation,
                "Source Error Statement Number": source_context.error_statement_number,
                "Source Error Statement": source_context.error_statement,
                "Source Type": f"{target_db}-Specific" if source_context.error_statement_number == 0 else f"{source_db} Source",
                "Target Error Statement Number": target_error_context.error_statement_number,
                "Original Target Error Statement": target_error_context.error_statement,
                "Corrected Target Error Statement": corrected_error_stmt,
                "Target Type": f"{target_db} Target",
                "Timestamp": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            # Add functional validation data if available
            if functional_validation:
                data_row.update({
                    "Business Logic Preserved": functional_validation.business_logic_preserved,
                    "Data Transformations Equivalent": functional_validation.data_transformations_equivalent,
                    "Conditional Logic Maintained": functional_validation.conditional_logic_maintained,
                    "Potential Behavior Differences": functional_validation.potential_behavior_differences
                })

            data = [data_row]

            # Get conversion path from state
            conversion_path = get_conversion_path_from_state(state) if state else ""

            # Use generic function to save data
            save_to_target_excel(conversion_path, target_statement_number, current_attempt, "Conversion_Validation", data, "conversion validation results")

        except Exception as e:
            print(f"⚠️ Could not save conversion validation results: {e}")

    def validateConversionWithAI(self, source_context: ErrorContext, target_error_context: ErrorContext, corrected_error_stmt: str, target_statements: List[str] = None) -> Dict[str, Any]:
        """Use AI to validate if the converted statement is correct compared to the source statement using structured output."""
        print(f"🧠 Using AI to validate conversion...")

        # Create prompt for AI using src_tgt validation approach
        prompt = create_src_tgt_validation_prompt(source_context, target_error_context, corrected_error_stmt, target_statements)

        # Use structured output for reliable parsing
        structured_llm = self.llm.client.with_structured_output(ValidationOutput)
        ai_result = structured_llm.invoke(prompt)

        is_correct = ai_result.is_correct
        explanation = ai_result.explanation
        confidence = ai_result.confidence

        # Create result
        result = {
            'is_correct': is_correct,
            'explanation': explanation,
            'confidence': confidence
        }

        print(f"🎯 AI validation result: correct={is_correct}, confidence={confidence}")
        print(f"📝 Explanation: {explanation}")

        return result

    def validate_conversion(self, state: WorkflowState) -> Dict[str, Any]:
        """Validate if the AI-generated corrections are correct compared to the source statement."""
        try:
            print("🔍 Starting conversion validation...")

            # Get the source context, target error context, AI corrections, and functional validation
            source_context = state.source_context
            target_error_context = state.target_error_context
            ai_corrections = getattr(state, 'ai_corrections', None)
            functional_validation = getattr(state, 'functional_validation', None)
            target_specific_optimization = getattr(state, 'target_specific_optimization', False)

            # Get current conversion attempts count
            conversion_attempts = getattr(state, 'conversion_attempts', 0) + 1
            print(f"📊 Conversion validation attempt {conversion_attempts}")

            if target_specific_optimization:
                # Get dynamic database names
                db_terms = get_database_specific_terms()
                target_db = db_terms['target_db']
                print(f"🚀 OPTIMIZATION: Validating target-specific conversion ({target_db} expertise applied)")
            else:
                print("🔄 STANDARD: Validating source-to-target conversion")

            if not source_context:
                print("⚠️ No source context to validate against")
                return {
                    "conversion_successful": False,
                    "conversion_attempts": conversion_attempts
                }

            if not target_error_context:
                print("⚠️ No target error context to validate")
                return {
                    "conversion_successful": False,
                    "conversion_attempts": conversion_attempts
                }

            if not ai_corrections:
                print("⚠️ No AI corrections to validate")
                return {
                    "conversion_successful": False,
                    "conversion_attempts": conversion_attempts
                }

            print(f"📊 Validating {len(ai_corrections)} AI corrections for error statement #{target_error_context.error_statement_number}")

            # Find the corrected error statement from AI corrections
            error_stmt_num = target_error_context.error_statement_number
            corrected_error_stmt = None

            for correction in ai_corrections:
                if correction.statement_number == error_stmt_num:
                    corrected_error_stmt = correction.corrected_statement
                    break

            if not corrected_error_stmt:
                print(f"⚠️ AI did not return correction for error statement #{error_stmt_num}")
                print(f"🔍 AI returned corrections for: {[f'#{c.statement_number}({c.statement_type})' for c in ai_corrections]}")
                print(f"❌ AI must return the exact statement number {error_stmt_num} for error_statement")
                return {
                    "conversion_successful": False,
                    "conversion_attempts": conversion_attempts
                }

            # Use single AI validation - simple and effective
            validation_result = self.validateConversionWithAI(
                source_context,
                target_error_context,
                corrected_error_stmt
            )

            # Extract validation success and explanation
            conversion_successful = validation_result.get('is_correct', False)
            explanation = validation_result.get('explanation', 'No explanation provided')
            confidence = validation_result.get('confidence', 0.0)

            # Save validation results to Excel with functional validation data
            self.saveConversionValidationToExcel(
                source_context,
                target_error_context,
                corrected_error_stmt,
                conversion_successful,
                explanation,
                state,
                functional_validation
            )

            if conversion_successful:
                print(f"✅ Conversion validated successfully with confidence {confidence}")
                return {
                    "conversion_successful": True,
                    "conversion_attempts": conversion_attempts,
                    "conversion_feedback": None  # Clear feedback on success
                }
            else:
                print(f"❌ Conversion validation failed with confidence {confidence}")
                print(f"📝 Conversion feedback: {explanation}")

                # Return the result along with the updated attempts count and feedback
                return {
                    "conversion_successful": False,
                    "conversion_attempts": conversion_attempts,
                    "conversion_feedback": explanation  # Store failure feedback
                }

        except Exception as e:
            print(f"❌ Error in conversion validation: {str(e)}")
            # Get current conversion attempts count even in case of error
            conversion_attempts = getattr(state, 'conversion_attempts', 0) + 1
            return {
                "conversion_successful": False,
                "conversion_attempts": conversion_attempts,
                "conversion_feedback": f"Validation error: {str(e)}"  # Store error as feedback
            }

    def deployment_status(self, state: WorkflowState) -> Dict[str, Any]:
        """
        Check the deployment status and determine if the workflow should end.

        NEW Target Statement-Based Logic:
            - If deployment successful: End workflow and save final SQL
            - If deployment failed with same target statement: Check attempt limit
            - If deployment failed with different target statement: Reset attempts and continue
            - If max attempts reached for same target statement: End workflow
        """
        try:
            print("🔄 Checking deployment status...")

            # Get the deployment status and target statement tracking info
            deployment_successful = state.deployment_successful
            error_message = getattr(state, 'error_message', None)
            updated_target_code = getattr(state, 'updated_target_code', None)
            source_code = getattr(state, 'source_code', None)
            source_statements = getattr(state, 'source_statements', [])
            target_statements = getattr(state, 'target_statements', [])
            target_position_mapper = getattr(state, 'target_position_mapper', None)

            # Target statement tracking fields
            target_statement_number = getattr(state, 'target_statement_number', None)
            current_attempt = getattr(state, 'current_attempt_for_statement', 1)
            max_attempt_per_statement = getattr(state, 'max_attempt_per_statement', 5)

            print(f"📊 Target Statement: #{target_statement_number}, Attempt: {current_attempt}/{max_attempt_per_statement}")
                        # Insert deployment record into database
            try:
                print("🔄 Inserting deployment record into database...")
                
                # Get source context
                source_context = getattr(state, 'source_context', None)
                
                # Extract source and target statements from contexts
                source_statement = source_context.error_statement if source_context else None

                # Use original target statement for database insertion (not modified target statement)
                original_target_statements = getattr(state, 'original_target_statements', None)
                target_statement = None
                if original_target_statements and target_statement_number and 1 <= target_statement_number <= len(original_target_statements):
                    target_statement = original_target_statements[target_statement_number - 1]
                    print(f"🔄 Using original target statement #{target_statement_number} for database insertion")

                converted_statement = None
                if target_statements and target_statement_number and 1 <= target_statement_number <= len(target_statements):
                    converted_statement = target_statements[target_statement_number - 1]
                # Prepare parameters for database insert
                target_object_id = getattr(state, 'target_object_id', None)
                
                if target_object_id and target_statement_number:
                    # Get database connection
                    connection = connect_database(state.project_db_credentials)
                    
                    # Call database insert function
                    success = conversion_agent_deployment_insert(
                        connection=connection,
                        tgt_id=target_object_id,
                        statement_number=target_statement_number,
                        attempt=current_attempt,
                        source_statement=source_statement,
                        target_statement=target_statement,
                        converted_statement=converted_statement,
                        observations=None,
                        is_deployed=deployment_successful,
                        error=error_message if not deployment_successful else None
                    )
                    
                    if success:
                        print(f"✅ Successfully inserted deployment record - tgt_id: {target_object_id}, statement: #{target_statement_number}, attempt: {current_attempt}")
                    else:
                        print(f"❌ Failed to insert deployment record")
                        
                    # Close connection
                    connection.close()
                else:
                    print("⚠️ Skipping database insert - tgt_id or target_statement_number not available")
                    
            except Exception as db_error:
                print(f"❌ Database insert error: {str(db_error)}")
                # Continue with workflow even if database insert fails
            

            if deployment_successful:
                print(f"✅ Deployment successful for Target Statement #{target_statement_number}, Attempt #{current_attempt}")
                print(f"🎉 Workflow completed successfully!")

                # Save final SQL file for this target statement
                if target_statement_number and updated_target_code:
                    conversion_path = get_conversion_path_from_state(state)
                    save_target_statement_sql_file(conversion_path, target_statement_number, updated_target_code)

                return {
                    "deployment_successful": deployment_successful,
                    "max_attempts_reached_for_statement": False,
                    "target_statement_number": target_statement_number,
                    "final_attempt_count": current_attempt
                }
            else:
                print(f"❌ Deployment failed for Target Statement #{target_statement_number}, Attempt #{current_attempt}: {error_message}")

                # CRITICAL: Check max attempts BEFORE allowing workflow to continue
                # This prevents the workflow from continuing to error analysis and finding a different statement
                if current_attempt >= max_attempt_per_statement:
                    print(f"🛑 Maximum attempts reached for Target Statement #{target_statement_number}!")
                    print(f"📊 Final status: Deployment failed after {max_attempt_per_statement} attempts for statement #{target_statement_number}")
                    print(f"🔚 Workflow ending due to max attempts for this target statement")

                    # Save final SQL file even on failure for debugging
                    if target_statement_number and updated_target_code:
                        conversion_path = get_conversion_path_from_state(state)
                        save_target_statement_sql_file(conversion_path, target_statement_number, updated_target_code)

                    return {
                        "deployment_successful": False,
                        "error_message": f"Max attempts ({max_attempt_per_statement}) reached for Target Statement #{target_statement_number}. Final error: {error_message}",
                        "max_attempts_reached_for_statement": True,
                        "target_statement_number": target_statement_number,
                        "final_attempt_count": current_attempt
                    }

                print(f"🔄 Continuing with next attempt for Target Statement #{target_statement_number}")
                print(f"📊 Will proceed to attempt #{current_attempt + 1}/{max_attempt_per_statement}")

                # Continue workflow - the attempt counter will be updated in validate_error_identification
                # when the target statement is validated again
                return {
                    "deployment_successful": deployment_successful,
                    "error_message": error_message,
                    "deployment_error": error_message,  # Use as input for next iteration
                    "target_code": updated_target_code,  # Use updated code as new target
                    "source_code": source_code,  # Keep original source code
                    "source_statements": source_statements,  # Preserve source statements for reuse
                    "target_statements": target_statements,  # Pass updated statements array
                    "target_position_mapper": target_position_mapper,  # Pass updated position mapper
                    "iteration_count": 1,  # Keep iteration count at 1 in state
                    "max_attempts_reached_for_statement": False,
                    "target_statement_number": target_statement_number,
                    "current_attempt_for_statement": current_attempt,
                    "max_attempt_per_statement": max_attempt_per_statement
                }

        except Exception as e:
            print(f"❌ Error in deployment status check: {str(e)}")
            return {
                "deployment_successful": False,
                "error_message": str(e),
                "max_attempts_reached_for_statement": False
            }


