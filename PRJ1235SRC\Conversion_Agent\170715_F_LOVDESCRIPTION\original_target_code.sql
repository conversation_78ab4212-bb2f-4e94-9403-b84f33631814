SET search_path TO AICCTEST;

CREATE OR REPLACE FUNCTION aicctest.F_LOVDESCRIPTION (IV_LOVIDS varchar)
    RETURNS varchar
    LANGUAGE plpgsql
    SECURITY DEFINER
    AS $BODY$
DECLARE
    V_LOVDESCRIPTION varchar(400);
    L1 record;
BEGIN
    SET search_path TO AICCTEST;
    FOR L1 IN (
        SELECT
            WL.LOVDETAILVALUE LOVDESCRIPTION
        FROM
            WARDS.Lovdetail WL
        WHERE
            WL.Lovdetailid IN (
                SELECT
                    regexp_split_to_table(IV_LOVIDS, ','));
ELSE
    V_LOVDESCRIPTION := L1.LOVDESCRIPTION;
END IF;
END LOOP;
    RETURN (V_LOVDESCRIPTION);
EXCEPTION
    WHEN NO_DATA_FOUND THEN
        RETURN NULL;
END;

$BODY$;

