"""
Context-Aware Statement Splitter Enhancement

This module provides enhanced statement splitting that recognizes structural dependencies
and logical statement groups to improve AI context understanding.
"""

from typing import List, Tuple, Dict
import re


class ContextAwareStatementGroup:
    """Represents a group of statements that are structurally dependent."""
    
    def __init__(self, statements: List[str], group_type: str, start_index: int):
        self.statements = statements
        self.group_type = group_type  # 'if_block', 'loop_block', 'case_block', etc.
        self.start_index = start_index
        self.end_index = start_index + len(statements) - 1


class ContextAwareStatementSplitter:
    """Enhanced statement splitter that recognizes structural dependencies."""
    
    def __init__(self):
        # Patterns for detecting structural dependencies
        self.control_patterns = {
            'if_block': {
                'start': r'\bIF\b.*\bTHEN\b',
                'middle': r'\bELSE\b|\bELSIF\b|\bELSEIF\b',
                'end': r'\bEND\s+IF\b'
            },
            'loop_block': {
                'start': r'\bFOR\b.*\bLOOP\b|\bWHILE\b.*\bLOOP\b',
                'end': r'\bEND\s+LOOP\b'
            },
            'case_block': {
                'start': r'\bCASE\b',
                'middle': r'\bWHEN\b',
                'end': r'\bEND\s+CASE\b'
            }
        }
    
    def identify_structural_dependencies(self, statements: List[str]) -> List[ContextAwareStatementGroup]:
        """
        Identify groups of statements that are structurally dependent.
        
        Args:
            statements: List of individual statements
            
        Returns:
            List of statement groups that should be processed together
        """
        groups = []
        i = 0
        
        while i < len(statements):
            stmt = statements[i].strip()
            
            # Check for control structure starts
            group_found = False
            for group_type, patterns in self.control_patterns.items():
                if re.search(patterns['start'], stmt, re.IGNORECASE):
                    # Found start of a control structure
                    group_statements = [stmt]
                    j = i + 1
                    
                    # Look for the end of this control structure
                    while j < len(statements):
                        next_stmt = statements[j].strip()
                        group_statements.append(next_stmt)
                        
                        if re.search(patterns['end'], next_stmt, re.IGNORECASE):
                            # Found the end
                            break
                        j += 1
                    
                    # Create the group
                    group = ContextAwareStatementGroup(group_statements, group_type, i)
                    groups.append(group)
                    i = j + 1
                    group_found = True
                    break
            
            if not group_found:
                i += 1
        
        return groups
    
    def get_enhanced_context(self, error_statement_index: int, statements: List[str]) -> Dict:
        """
        Get enhanced context that includes structural dependency information.
        
        Args:
            error_statement_index: Index of the error statement
            statements: List of all statements
            
        Returns:
            Enhanced context with structural dependency information
        """
        groups = self.identify_structural_dependencies(statements)
        
        # Find if error statement is part of a structural group
        error_group = None
        for group in groups:
            if group.start_index <= error_statement_index <= group.end_index:
                error_group = group
                break
        
        context = {
            'error_statement_index': error_statement_index,
            'is_part_of_group': error_group is not None,
            'group_type': error_group.group_type if error_group else None,
            'group_statements': error_group.statements if error_group else None,
            'structural_dependency': self._analyze_structural_dependency(
                error_statement_index, statements, error_group
            )
        }
        
        return context
    
    def _analyze_structural_dependency(self, error_index: int, statements: List[str], group: ContextAwareStatementGroup = None) -> Dict:
        """Analyze the structural dependency of the error statement."""
        if not group:
            return {'type': 'independent', 'requires_context': False}
        
        error_stmt = statements[error_index].strip()
        
        # Check for common dependency patterns
        if re.search(r'\bELSE\b', error_stmt, re.IGNORECASE):
            return {
                'type': 'else_clause',
                'requires_context': True,
                'needs': 'IF statement',
                'suggestion': 'Include complete IF-ELSE structure in correction'
            }
        
        if re.search(r'\bEND\s+(IF|LOOP|CASE)\b', error_stmt, re.IGNORECASE):
            return {
                'type': 'end_clause',
                'requires_context': True,
                'needs': 'Opening control structure',
                'suggestion': 'Ensure proper opening structure exists'
            }
        
        if re.search(r'\bWHEN\b', error_stmt, re.IGNORECASE):
            return {
                'type': 'when_clause',
                'requires_context': True,
                'needs': 'CASE statement',
                'suggestion': 'Include complete CASE structure in correction'
            }
        
        return {'type': 'independent', 'requires_context': False}


def enhance_conversion_context(before_stmt: str, error_stmt: str, after_stmt: str) -> str:
    """
    Generate enhanced context information for AI conversion.
    
    Args:
        before_stmt: Statement before error
        error_stmt: Error statement
        after_stmt: Statement after error
        
    Returns:
        Enhanced context description for AI
    """
    splitter = ContextAwareStatementSplitter()
    statements = [before_stmt, error_stmt, after_stmt]
    
    # Analyze structural dependencies
    context = splitter.get_enhanced_context(1, statements)  # Error is at index 1
    
    if context['is_part_of_group']:
        dependency = context['structural_dependency']
        if dependency['requires_context']:
            return f"""
STRUCTURAL DEPENDENCY DETECTED:
- Error statement type: {dependency['type']}
- Requires: {dependency['needs']}
- Suggestion: {dependency['suggestion']}
- Group type: {context['group_type']}

CONTEXT ANALYSIS:
The error statement cannot be fixed in isolation. It requires understanding of the complete {context['group_type']} structure.
Consider the before/after statements as part of the same logical unit.
"""
    
    return "No structural dependencies detected. Statement can be fixed independently."


# Example usage for testing
if __name__ == "__main__":
    # Test case from F_LOVDESCRIPTION
    before = "FOR L1 IN (SELECT ... FROM ... WHERE ...) LOOP"
    error = "ELSE V_LOVDESCRIPTION := L1.LOVDESCRIPTION;"
    after = "END IF;"
    
    enhanced_context = enhance_conversion_context(before, error, after)
    print("Enhanced Context:")
    print(enhanced_context)
