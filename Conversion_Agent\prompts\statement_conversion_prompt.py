"""
Prompts for statement conversion in database conversion.
"""
from typing import Dict
from Conversion_Agent.utils.database_names import get_database_specific_terms

def create_statement_conversion_prompt(source_context: Dict, target_error_context: Dict, error_message: str, target_statements: list = None, previous_feedback: str = None, attempt_history: dict = None, current_statement_number: int = None) -> str:
    """
    Creates a focused prompt for fixing only the specific deployment error with attempt history learning.

    This function creates a prompt that instructs the LLM to fix ONLY the specific
    deployment error mentioned in the error message, without over-correcting other issues.

    Args:
        source_context: Dictionary containing the source context (before, error, after statements)
        target_error_context: Dictionary containing the target error context
        error_message: The error message from deployment
        target_statements: List of target statements for context (now original_target_statements)
        previous_feedback: Previous feedback if any
        attempt_history: Dict of attempt history per statement {statement_number: [last_10_attempts]}
        current_statement_number: Current target statement number being processed

    Returns:
        A focused prompt string for the LLM with attempt history context
    """
    # Get dynamic database names
    db_terms = get_database_specific_terms()
    source_db = db_terms['source_db']
    target_db = db_terms['target_db']
    expert_title = db_terms['expert_title']

    # Check if this is a target database-specific scenario (no source mapping)
    is_target_specific = (source_context.error_statement_number == 0 or
                         not source_context.error_statement or
                         source_context.error_statement.strip() == "")

    # Add attempt history section for cumulative learning (last 10 records for current statement)
    attempt_history_section = ""
    if attempt_history and current_statement_number and current_statement_number in attempt_history:
        statement_attempts = attempt_history[current_statement_number]

        if statement_attempts:
            # Get current attempt number from the latest attempt
            latest_attempt = statement_attempts[-1] if statement_attempts else {}
            current_attempt_num = latest_attempt.get('attempt_number', 1)
            next_attempt_num = current_attempt_num + 1 if current_attempt_num else 1

            attempt_history_section = f"""
PREVIOUS ATTEMPTS LEARNING (Last {len(statement_attempts)} attempts for Statement #{current_statement_number}):
You are now working on Attempt #{next_attempt_num}. Learn from recent attempts to avoid repeating mistakes:

"""
            # Show all available attempts (up to last 10) with proper field mapping
            for i, attempt in enumerate(statement_attempts, 1):
                phase = attempt.get('phase', 'Unknown')
                attempt_num = attempt.get('attempt_number', 'N/A')
                timestamp = attempt.get('timestamp', 'N/A')

                # Handle different field names based on phase
                if phase == "statement_conversion":
                    corrections_info = f"Generated {attempt.get('corrections_made', 'N/A')} corrections"
                    details = attempt.get('explanation', 'N/A')
                elif phase == "deployment":
                    corrections_info = f"Applied {attempt.get('corrections_applied', 'N/A')} corrections"
                    details = f"Status: {attempt.get('deployment_status', 'N/A')}"
                elif phase == "error_identification_validation":
                    corrections_info = f"Result: {attempt.get('validation_result', 'N/A')}"
                    details = attempt.get('explanation', 'N/A')
                else:
                    corrections_info = "N/A"
                    details = attempt.get('explanation', 'N/A')

                # Include deployment error if available
                deployment_error = attempt.get('deployment_error', '')
                error_info = f"\n- Deployment Error: {deployment_error}" if deployment_error and deployment_error != 'N/A' else ""

                attempt_history_section += f"""
Recent Attempt {i} (#{attempt_num}) - {phase}:
- Timestamp: {timestamp}
- {corrections_info}
- Details: {details}{error_info}

"""

            attempt_history_section += """
CUMULATIVE LEARNING INSTRUCTIONS:
1. Analyze patterns from recent attempts - what was tried before?
2. Identify why previous corrections may have failed based on deployment errors
3. Build upon recent learning without repeating same mistakes
4. Consider alternative approaches based on recent attempt history
5. Focus on what hasn't been tried yet for this specific error
6. Use original target statements as reference (never modified)
7. Learn from the most recent patterns and avoid similar approaches that failed
8. Always provide complete, executable code implementations
9. If previous attempts used incomplete implementations, provide fully working solutions
10. Pay special attention to deployment errors from previous attempts to avoid same issues

"""

    # Add feedback section if provided (integrated with attempt history)
    feedback_section = ""
    if previous_feedback:
        feedback_section = f"""
IMMEDIATE PREVIOUS FEEDBACK:
The most recent conversion attempt was rejected with this feedback:
{previous_feedback}

CRITICAL: Address this specific feedback in your correction while also learning from the attempt history above.
- Fix the exact problems mentioned in the feedback
- Focus only on the deployment error, not other issues
- Ensure proper target database syntax for the specific error
- {"Apply target database expertise directly" if is_target_specific else "Use source context as reference"}
- Consider why previous attempts failed based on the attempt history patterns

"""

    if is_target_specific:
        # Target database-specific conversion prompt with attempt history
        return f"""🚨 FOCUSED ERROR FIXING APPROACH 🚨
You are a {target_db} Expert with deep knowledge of SQL conversion patterns. Fix ONLY the specific deployment error mentioned in the error message.

CRITICAL RULES:
- Fix ONLY the specific error mentioned in the error message
- Do NOT fix other potential issues unless they are causing the same error

🚨 NAMING PRESERVATION PRIORITY:
- NEVER change variable/column names from source context (maxRequestNumber stays maxRequestNumber)
- NEVER convert naming conventions (camelCase stays camelCase, snake_case stays snake_case)
- Source context naming is AUTHORITATIVE - copy identifiers exactly from source
- If source has "maxRequestNumber", corrected statement MUST have "maxRequestNumber"

- Validate that corrected logic produces IDENTICAL business outcomes as source
- Verify all conditional logic, loops, and data transformations remain equivalent
- Ensure database-specific function behavior is properly converted
- Provide only complete, executable {target_db} code
- Learn from recent attempts to avoid repeating mistakes
- Never use placeholder text or incomplete code implementations
- Always provide complete, executable code implementations
- If you need to implement logic, provide concrete {target_db} syntax

EXPERT SQL CONVERSION KNOWLEDGE:
Apply your extensive expertise in {source_db} to {target_db} conversion patterns. Transform database-specific syntax while preserving identical business logic and functionality.

FUNCTIONAL LOGIC VALIDATION:
1. **Data Flow**: Ensure same data transformations occur
2. **Conditional Logic**: Preserve all IF/CASE/WHEN conditions exactly
3. **Calculations**: Verify mathematical operations produce identical results
4. **Aggregations**: Ensure GROUP BY, SUM, COUNT logic remains equivalent
5. **Joins**: Validate relationship logic is functionally identical
6. **Filters**: Confirm WHERE clauses filter same data sets
7. **NULL Handling**: Verify NULL processing behavior is equivalent
8. **Error Conditions**: Maintain identical error handling and edge cases

{attempt_history_section}

{feedback_section}

ERROR MESSAGE:
{error_message}

TARGET ERROR CONTEXT (from original target statements):
Before Error (#{target_error_context.before_statement_number}):
{target_error_context.before_statement}

Error Statement (#{target_error_context.error_statement_number}):
{target_error_context.error_statement}

After Error (#{target_error_context.after_statement_number}):
{target_error_context.after_statement}

FOCUSED ANALYSIS APPROACH:
1. **Review recent attempt history** - understand what was tried recently and why it failed
2. **Use the error position** (if provided) to locate the exact problematic syntax in the statement
3. **Identify the specific syntax causing the error** from the error message
4. **Apply minimal fix** to resolve only that specific syntax issue
5. **If the EXACT SAME syntax error appears multiple times**, fix ALL instances of that exact syntax only
6. **DO NOT fix SIMILAR but different issues** - only fix the exact syntax mentioned in the deployment error
7. **Preserve all other code** exactly as it is, including similar-looking but different syntax
8. **FUNCTIONAL VALIDATION**: Verify corrected statement maintains identical business logic
9. **OUTCOME VERIFICATION**: Ensure same input produces same output as source
10. **Consider alternative approaches** if recent attempts failed with similar fixes

TASK:
SURGICAL FIX APPROACH - Fix ONLY the exact syntax issue mentioned in the deployment error message.

CRITICAL INSTRUCTIONS:
1. **IDENTIFY**: Find the exact problematic syntax from the error message
2. **LOCATE**: Use position information to find the exact location in the statement
3. **REPLACE**: Replace ONLY that specific syntax with valid {target_db} syntax
4. **PRESERVE**: Keep everything else EXACTLY as it is, including other potential issues
5. **NO OVER-CORRECTION**: Do not fix other issues even if they exist in the same statement

OUTPUT FORMAT (JSON):
{{
  "corrected_statements": [
    {{
      "statement_number": <integer>,
      "original_statement": "<original statement>",
      "corrected_statement": "<corrected statement>",
      "statement_type": "before_error|error_statement|after_error",
      "changes_made": "<description of the specific change made to fix the deployment error while preserving source naming>"
    }}
  ],
  "explanation": "<brief explanation of what specific error was fixed and how>",
  "functional_validation": {{
    "business_logic_preserved": true/false,
    "data_transformations_equivalent": true/false,
    "conditional_logic_maintained": true/false,
    "potential_behavior_differences": "<description of any concerns or 'none'>"
  }}
}}

IMPORTANT:
- Fix ONLY the specific error type mentioned in the deployment error message
- If the same error pattern appears multiple times in the statement, fix ALL instances of that pattern
- DO NOT fix different types of issues - only the specific error type from the deployment error
- Keep before/after statements unchanged (set corrected_statement = original_statement)
- For error statement: Apply minimal fix to resolve the specific error type only"""

    else:
        # Standard conversion prompt with attempt history
        return f"""🚨 FOCUSED ERROR FIXING APPROACH 🚨
You are a {expert_title} with deep knowledge of SQL conversion patterns. Fix ONLY the specific deployment error mentioned in the error message.

CRITICAL RULES:
- Fix ONLY the specific error mentioned in the error message
- Do NOT fix other potential issues unless they are causing the same error

🚨 NAMING PRESERVATION PRIORITY:
- NEVER change variable/column names from source context (maxRequestNumber stays maxRequestNumber)
- NEVER convert naming conventions (camelCase stays camelCase, snake_case stays snake_case)
- Source context naming is AUTHORITATIVE - copy identifiers exactly from source
- If source has "maxRequestNumber", corrected statement MUST have "maxRequestNumber"

- Validate that corrected logic produces IDENTICAL business outcomes as source
- Verify all conditional logic, loops, and data transformations remain equivalent
- Ensure database-specific function behavior is properly converted
- Use source context to understand the intended functionality
- Provide only complete, executable {target_db} code
- Learn from recent attempts to avoid repeating mistakes
- Never use placeholder text or incomplete code implementations

EXPERT SQL CONVERSION KNOWLEDGE:
Apply your extensive expertise in {source_db} to {target_db} conversion patterns. Transform database-specific syntax while preserving identical business logic and functionality.

FUNCTIONAL LOGIC VALIDATION:
1. **Data Flow**: Ensure same data transformations occur
2. **Conditional Logic**: Preserve all IF/CASE/WHEN conditions exactly
3. **Calculations**: Verify mathematical operations produce identical results
4. **Aggregations**: Ensure GROUP BY, SUM, COUNT logic remains equivalent
5. **Joins**: Validate relationship logic is functionally identical
6. **Filters**: Confirm WHERE clauses filter same data sets
7. **NULL Handling**: Verify NULL processing behavior is equivalent
8. **Error Conditions**: Maintain identical error handling and edge cases

{attempt_history_section}

{feedback_section}

ERROR MESSAGE: {error_message}

SOURCE CONTEXT ({source_db}) - NAMING AUTHORITY:
Before Error (#{source_context.before_statement_number}): {source_context.before_statement}
Error Statement (#{source_context.error_statement_number}): {source_context.error_statement}
After Error (#{source_context.after_statement_number}): {source_context.after_statement}

🚨 NAMING EXAMPLE: If source has "maxRequestNumber", your correction MUST use "maxRequestNumber" (NOT max_request_number or MaxRequestNumber)

🚨 STRUCTURAL DEPENDENCY EXAMPLES:
- Context: Before="FOR L1 IN (...)", Error="LOOP", After="ELSE stmt" → Fix: "FOR L1 IN (...) LOOP IF condition THEN stmt ELSE stmt END IF;"
- Context: Before="IF condition", Error="THEN stmt", After="ELSE stmt" → Fix: "IF condition THEN stmt" (complete IF structure)
- Context: Before="CASE expr", Error="WHEN value", After="END" → Fix: "CASE expr WHEN value THEN stmt END CASE;"

TARGET ERROR CONTEXT ({target_db} - from original target statements):
Before Error (#{target_error_context.before_statement_number}): {target_error_context.before_statement}
Error Statement (#{target_error_context.error_statement_number}): {target_error_context.error_statement}
After Error (#{target_error_context.after_statement_number}): {target_error_context.after_statement}

🚨 CRITICAL CONTEXT DEPENDENCY ANALYSIS:
Before proceeding, analyze if the error statement is contextually dependent:

**DEPENDENCY INDICATORS:**
- Error statement contains: ELSE, END IF, END LOOP, END CASE, WHEN (without CASE)
- Before statement shows: FOR...LOOP, IF...THEN, CASE, incomplete control structure
- After statement shows: ELSE, END IF, END LOOP, continuation of control structure

**IF DEPENDENCY DETECTED:**
- The error statement CANNOT be fixed in isolation
- You MUST include the complete control structure in your correction
- Example: If error="ELSE stmt" and before="FOR...LOOP", then correction must be "FOR...LOOP IF condition THEN stmt ELSE stmt END IF;"

FOCUSED ANALYSIS APPROACH:
1. **Review recent attempt history** - understand what was tried recently and why it failed
2. **Use the error position** (if provided) to locate the exact problematic syntax in the statement
3. **Identify the specific syntax causing the error** from the error message
4. **Reference the source context** to understand the intended functionality
5. **EXTRACT ALL IDENTIFIERS** from source context (variables, columns, functions)
6. **NAMING VALIDATION**: Ensure corrected statement uses EXACT same identifiers as source
7. **STRUCTURAL DEPENDENCY ANALYSIS**: Check if error statement depends on before/after statements
   - IF error statement is ELSE/END IF/END LOOP → check for missing IF/LOOP structure
   - IF before/after statements show incomplete control structures → complete them in error statement
   - IF statements are structurally linked → fix them as a cohesive unit
8. **Apply minimal {target_db} fix** to resolve only that specific syntax issue
9. **Preserve all other code** exactly as it is, including similar-looking but different syntax
10. **FUNCTIONAL VALIDATION**: Verify corrected statement maintains identical business logic
11. **OUTCOME VERIFICATION**: Ensure same input produces same output as source
12. **Consider alternative approaches** if recent attempts failed with similar fixes

TASK:
SURGICAL FIX APPROACH - Fix ONLY the exact syntax issue mentioned in the deployment error message.

CRITICAL INSTRUCTIONS:
1. **IDENTIFY**: Find the exact problematic syntax from the error message
2. **LOCATE**: Use position information to find the exact location in the statement
3. **REPLACE**: Replace ONLY that specific syntax with valid {target_db} syntax
4. **PRESERVE**: Keep everything else EXACTLY as it is, including other potential issues
5. **NO OVER-CORRECTION**: Do not fix other issues even if they exist in the same statement

SPECIFIC ERROR ANALYSIS:
1. **Use error position** (if provided) to pinpoint the exact problematic syntax
2. **Identify the exact syntax causing the error** from the error message
3. **Apply minimal {target_db} fix** to resolve only that specific syntax issue
4. **If the EXACT SAME syntax error appears multiple times**, fix ALL instances of that exact syntax only
5. **DO NOT fix DIFFERENT syntax issues** - only fix the exact syntax mentioned in the deployment error
6. **Preserve all other code** exactly as it is, including similar-looking but different syntax

CRITICAL DISTINCTION:
- ✅ **EXACT SAME ERROR**: Fix only the exact syntax issue mentioned in the error message
- ❌ **SIMILAR BUT DIFFERENT**: Do NOT fix similar-looking issues that are not the exact error mentioned
- **RULE**: Fix only the specific syntax/location mentioned in the deployment error message
- **FOCUS**: Target the exact problematic syntax from the error message, not similar patterns

OUTPUT FORMAT (JSON):
{{
  "corrected_statements": [
    {{
      "statement_number": <integer>,
      "original_statement": "<original statement>",
      "corrected_statement": "<corrected statement>",
      "statement_type": "before_error|error_statement|after_error",
      "changes_made": "<description of the specific change made to fix the deployment error while preserving source naming>"
    }}
  ],
  "explanation": "<brief explanation of what specific error was fixed and how>",
  "functional_validation": {{
    "business_logic_preserved": true/false,
    "data_transformations_equivalent": true/false,
    "conditional_logic_maintained": true/false,
    "potential_behavior_differences": "<description of any concerns or 'none'>"
  }}
}}

IMPORTANT:
- Fix ONLY the specific error type mentioned in the deployment error message
- If the same error pattern appears multiple times in the statement, fix ALL instances of that pattern
- DO NOT fix different types of issues - only the specific error type from the deployment error
- Keep before/after statements unchanged (set corrected_statement = original_statement)
- For error statement: Apply minimal fix to resolve the specific error type only

🚨 STRUCTURAL DEPENDENCY CHECK:
- IF error statement contains ELSE → ensure corresponding IF exists in corrected statement
- IF error statement contains END IF/END LOOP → ensure proper opening structure
- IF before/after statements show incomplete control flow → complete it in error statement
- IF statements are contextually linked → fix them as a complete structural unit

🚨 FINAL NAMING CHECK:
- Compare your corrected statement with source context
- Verify ALL variable/column names match source exactly
- If source has "maxRequestNumber", corrected MUST have "maxRequestNumber"
- NEVER change naming conventions during error fixing

- Return ALL three statements (before, error, after) in the corrected_statements array
- Use the EXACT statement numbers provided in the context:
  * Before Error: statement_number = {target_error_context.before_statement_number}
  * Error Statement: statement_number = {target_error_context.error_statement_number}
  * After Error: statement_number = {target_error_context.after_statement_number}
- **PRIMARY FOCUS: Fix ONLY the deployment error in the error statement**
- **CONTEXT STATEMENTS: Before/after statements are for context awareness only**
- **DO NOT MODIFY: Before/after statements - keep them exactly as original_statement**
- Only the error_statement correction will be applied to the deployed code
- Before/after statements provide business logic context but should remain unchanged
- For before/after statements: ALWAYS set corrected_statement equal to original_statement
- For error statement: Provide the complete alternative implementation that fixes the error
- Focus on fixing the specific deployment error while maintaining the same functionality"""
