SET search_path TO AICCTEST;

CREATE OR REPLACE FUNCTION aicctest.F_GETSERVICEIDFROMBLOODTYPE (in_bloodTypeid bb.bloodbag.bagtypeid%type, l_Paediatric numeric, l_Thalassemia numeric)
    RETURNS bb.requestdetails.serviceid%type
    LANGUAGE plpgsql
    SECURITY DEFINER
AS $BODY$
DECLARE
    l_service bb.requestdetails.serviceid%type;
l_bloodUnitName bb.bloodtypemaster.bloodtypename%type;
BEGIN
    SET search_path TO AICCTEST;
SELECT
        bloodtypename INTO STRICT l_bloodUnitName
    FROM
        bb.bloodtypemaster
    WHERE
        bloodtypeid = in_bloodTypeid;
CASE upper(l_bloodUnitName)
    WHEN 'RDP' THEN
        l_service := 17268;
WHEN 'RBC' THEN
        IF ((l_Paediatric = 1 AND l_Thalassemia = 1) OR (l_Paediatric = 1 AND l_Thalassemia = 0)) THEN
                l_service := 6141;
ELSE
        IF (l_Paediatric = 0 AND l_Thalassemia = 1) THEN
                l_service := 6162;
ELSE
        l_service := 19227;
END IF;
END IF;
WHEN 'FP' THEN
        l_service := '';
WHEN 'WB' THEN
        IF l_Paediatric = 1 THEN
            l_service := 6151;
ELSE
            l_service := 70927;
END IF;
WHEN 'CRYO' THEN
        l_service := 17266;
WHEN 'FFP' THEN
        l_service := 17269;
WHEN 'CPP' THEN
        l_service := '27779';
WHEN 'GRAN' THEN
        l_service := 5513;
WHEN 'WRBC' THEN
        IF l_Thalassemia = 1 THEN
            l_service := 6162;
ELSE
            l_service := 17267;
END IF;
WHEN 'PRP' THEN
        l_service := 17268;
WHEN 'SDP' THEN
        l_service := 14438;
WHEN 'PRC' THEN
        l_service := 70954;
WHEN 'CP' THEN
        l_service := 70952;
WHEN 'PRC4' THEN
        l_service := 70951;
END CASE;
RETURN l_service;
END;$BODY$;