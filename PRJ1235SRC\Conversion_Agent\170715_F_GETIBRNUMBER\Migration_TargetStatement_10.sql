SET search_path TO AICCTEST;

CREATE OR REPLACE FUNCTION aicctest.F_GETIBRNUMBER ()
    RETURNS bb.bloodunitrequest.requestnumber%type
    LANGUAGE plpgsql
    SECURITY DEFINER
AS $BODY$
DECLARE
    max_request_number bb.bloodunitrequest.requestnumber%type;
max_request_no numeric;
requestNo numeric;
requestNoString bb.bloodunitrequest.requestnumber%type;
internalRequestNumber bb.bloodunitrequest.requestnumber%type;
requestNumber bb.bloodunitrequest.requestnumber%type;
BEGIN
    SET search_path TO AICCTEST;
requestNumber := 'IBR';
DECLARE maxRequestNumber TEXT; SELECT MAX(RequestNumber) INTO STRICT maxRequestNumber FROM bb.Bloodunitrequest b WHERE b.requestnumber LIKE 'IBR%' AND b.status IN (0, 1);
IF NOT (length(maxRequestNumber) = 0 OR maxRequestNumber = '0') THEN
        IF length(maxRequestNumber) = 9 THEN
            maxRequestNumber := SUBSTR(maxRequestNumber, 4, 7);
ELSE
            IF length(maxRequestNumber) < 9 THEN
                maxRequestNumber := SUBSTR(maxRequestNumber, 4);
END IF;
END IF;
maxRequestNo := maxRequestNumber;
IF maxRequestNo = 999999 THEN
            requestNo := 1;
ELSE
            requestNo := maxRequestNo + 1;
END IF;
ELSE
        requestNo := 1;
END IF;
requestNoString := requestNo;
While length(requestNoString) < 6 LOOP
        requestNoString := '0' || requestNoString;
END LOOP;
requestNumber := requestNumber || requestNoString;
internalRequestNumber := requestNumber;
RETURN (internalRequestNumber);
END;$BODY$;